import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import { Colors } from '../../src/constants/Colors';
import { getLayoutConfig } from '../../src/utils/responsive';

export default function TabLayout() {
  const layoutConfig = getLayoutConfig();
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarStyle: {
          backgroundColor: Colors.background,
          borderTopColor: Colors.border,
          borderTopWidth: 1,
          paddingBottom: layoutConfig.isTablet ? 8 : 5,
          paddingTop: layoutConfig.isTablet ? 8 : 5,
          height: layoutConfig.isTablet ? 80 : 60,
          paddingHorizontal: layoutConfig.padding,
        },
        tabBarLabelStyle: {
          fontSize: layoutConfig.isTablet ? 14 : 12,
          fontWeight: '600',
        },
        tabBarIconStyle: {
          marginBottom: layoutConfig.isTablet ? 4 : 2,
        },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => (
            <Ionicons name="home" size={layoutConfig.isTablet ? 28 : 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="scan"
        options={{
          title: 'Scan',
          tabBarIcon: ({ color }) => (
            <Ionicons name="camera" size={layoutConfig.isTablet ? 28 : 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="history"
        options={{
          title: 'History',
          tabBarIcon: ({ color }) => (
            <Ionicons name="time" size={layoutConfig.isTablet ? 28 : 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="todo"
        options={{
          title: 'To-Do',
          tabBarIcon: ({ color }) => (
            <Ionicons name="checkmark-circle" size={layoutConfig.isTablet ? 28 : 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => (
            <Ionicons name="person" size={layoutConfig.isTablet ? 28 : 24} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
