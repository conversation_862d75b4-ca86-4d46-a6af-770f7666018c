import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, getColorByCategory } from '../../src/constants/Colors';
import { useAppStore } from '../../src/store/useAppStore';
import { Card } from '../../src/components';
import { ScanHistory } from '../../src/types';

export default function HistoryScreen() {
  const { scanHistory, updateScanInHistory } = useAppStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = ['all', 'food', 'plants', 'animals', 'rocks', 'coins'];

  const filteredHistory = scanHistory.filter((scan) => {
    const matchesSearch = scan.result.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         scan.result.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || selectedCategory === 'all' || 
                           scan.result.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFavorite = (scanId: string) => {
    const scan = scanHistory.find(s => s.id === scanId);
    if (scan) {
      updateScanInHistory(scanId, { isFavorite: !scan.isFavorite });
    }
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'food': return 'restaurant';
      case 'plants': return 'leaf';
      case 'animals': return 'paw';
      case 'rocks': return 'diamond';
      case 'coins': return 'logo-bitcoin';
      default: return 'help-circle';
    }
  };

  const renderScanItem = ({ item }: { item: ScanHistory }) => (
    <Card style={styles.scanCard} onPress={() => {/* TODO: Navigate to detail */}}>
      <View style={styles.scanHeader}>
        <View style={styles.scanInfo}>
          <View style={[
            styles.categoryIcon,
            { backgroundColor: getColorByCategory(item.result.category) + '20' }
          ]}>
            <Ionicons
              name={getCategoryIcon(item.result.category) as any}
              size={20}
              color={getColorByCategory(item.result.category)}
            />
          </View>
          <View style={styles.scanDetails}>
            <Text style={styles.scanName}>{item.result.name}</Text>
            <Text style={styles.scanCategory}>
              {item.result.category} • {Math.round(item.result.confidence * 100)}% confidence
            </Text>
            <Text style={styles.scanDate}>
              {new Date(item.result.timestamp).toLocaleDateString()}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => toggleFavorite(item.id)}
        >
          <Ionicons
            name={item.isFavorite ? 'heart' : 'heart-outline'}
            size={24}
            color={item.isFavorite ? Colors.error : Colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
      
      {item.notes && (
        <Text style={styles.scanNotes} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
      
      {item.tags && item.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {item.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
          {item.tags.length > 3 && (
            <Text style={styles.moreTagsText}>+{item.tags.length - 3} more</Text>
          )}
        </View>
      )}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="time-outline" size={64} color={Colors.textLight} />
      <Text style={styles.emptyTitle}>No scan history</Text>
      <Text style={styles.emptySubtitle}>
        Start scanning items to build your history
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scan History</Text>
        <Text style={styles.headerSubtitle}>
          {scanHistory.length} item{scanHistory.length !== 1 ? 's' : ''} scanned
        </Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={Colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search scans..."
            placeholderTextColor={Colors.textLight}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Category Filter */}
      <View style={styles.categoriesContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={categories}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.categoryFilter,
                selectedCategory === item && styles.categoryFilterActive
              ]}
              onPress={() => setSelectedCategory(item === selectedCategory ? null : item)}
            >
              <Text style={[
                styles.categoryFilterText,
                selectedCategory === item && styles.categoryFilterTextActive
              ]}>
                {item.charAt(0).toUpperCase() + item.slice(1)}
              </Text>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.categoriesContent}
        />
      </View>

      {/* Scan History List */}
      <FlatList
        data={filteredHistory}
        keyExtractor={(item) => item.id}
        renderItem={renderScanItem}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
    marginLeft: 8,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoriesContent: {
    paddingHorizontal: 20,
  },
  categoryFilter: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.backgroundSecondary,
  },
  categoryFilterActive: {
    backgroundColor: Colors.primary,
  },
  categoryFilterText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  categoryFilterTextActive: {
    color: Colors.textInverse,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  scanCard: {
    marginBottom: 12,
  },
  scanHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  scanInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  scanDetails: {
    flex: 1,
  },
  scanName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  scanCategory: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 2,
    textTransform: 'capitalize',
  },
  scanDate: {
    fontSize: 12,
    color: Colors.textLight,
  },
  favoriteButton: {
    padding: 4,
  },
  scanNotes: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tag: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '600',
  },
  moreTagsText: {
    fontSize: 12,
    color: Colors.textLight,
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});
