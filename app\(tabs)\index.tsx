import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../src/constants/Colors';
import { useAppStore } from '../../src/store/useAppStore';

export default function DashboardScreen() {
  const { 
    scanHistory, 
    todoItems, 
    getRecentScans, 
    getUpcomingTasks,
    getCompletedTasksCount 
  } = useAppStore();

  const recentScans = getRecentScans(3);
  const upcomingTasks = getUpcomingTasks(3);
  const completedTasksCount = getCompletedTasksCount();

  const stats = [
    {
      title: 'Total Scans',
      value: scanHistory.length.toString(),
      icon: 'camera',
      color: Colors.primary,
    },
    {
      title: 'Completed Tasks',
      value: completedTasksCount.toString(),
      icon: 'checkmark-circle',
      color: Colors.success,
    },
    {
      title: 'Pending Tasks',
      value: (todoItems.length - completedTasksCount).toString(),
      icon: 'time',
      color: Colors.warning,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>BioScan</Text>
          <Text style={styles.headerSubtitle}>AI-Powered Identification</Text>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: stat.color + '20' }]}>
                <Ionicons name={stat.icon as any} size={24} color={stat.color} />
              </View>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statTitle}>{stat.title}</Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton}>
              <View style={[styles.actionIcon, { backgroundColor: Colors.primary + '20' }]}>
                <Ionicons name="camera" size={28} color={Colors.primary} />
              </View>
              <Text style={styles.actionText}>Scan Item</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <View style={[styles.actionIcon, { backgroundColor: Colors.accent + '20' }]}>
                <Ionicons name="add-circle" size={28} color={Colors.accent} />
              </View>
              <Text style={styles.actionText}>Add Task</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <View style={[styles.actionIcon, { backgroundColor: Colors.info + '20' }]}>
                <Ionicons name="search" size={28} color={Colors.info} />
              </View>
              <Text style={styles.actionText}>Search</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Scans */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Scans</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {recentScans.length > 0 ? (
            recentScans.map((scan) => (
              <TouchableOpacity key={scan.id} style={styles.scanItem}>
                <View style={[
                  styles.categoryIcon, 
                  { backgroundColor: getCategoryColor(scan.result.category) + '20' }
                ]}>
                  <Ionicons 
                    name={getCategoryIcon(scan.result.category)} 
                    size={20} 
                    color={getCategoryColor(scan.result.category)} 
                  />
                </View>
                <View style={styles.scanInfo}>
                  <Text style={styles.scanName}>{scan.result.name}</Text>
                  <Text style={styles.scanCategory}>{scan.result.category}</Text>
                </View>
                <Text style={styles.scanConfidence}>
                  {Math.round(scan.result.confidence * 100)}%
                </Text>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="camera-outline" size={48} color={Colors.textLight} />
              <Text style={styles.emptyText}>No scans yet</Text>
              <Text style={styles.emptySubtext}>Start by scanning your first item!</Text>
            </View>
          )}
        </View>

        {/* Upcoming Tasks */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {upcomingTasks.length > 0 ? (
            upcomingTasks.map((task) => (
              <TouchableOpacity key={task.id} style={styles.taskItem}>
                <View style={styles.taskInfo}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <Text style={styles.taskDue}>
                    Due: {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No date'}
                  </Text>
                </View>
                <View style={[
                  styles.priorityIndicator,
                  { backgroundColor: getPriorityColor(task.priority) }
                ]} />
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="checkmark-circle-outline" size={48} color={Colors.textLight} />
              <Text style={styles.emptyText}>No upcoming tasks</Text>
              <Text style={styles.emptySubtext}>Add tasks to stay organized!</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

function getCategoryColor(category: string): string {
  switch (category) {
    case 'food': return Colors.food;
    case 'plants': return Colors.plants;
    case 'animals': return Colors.animals;
    case 'rocks': return Colors.rocks;
    case 'coins': return Colors.coins;
    default: return Colors.primary;
  }
}

function getCategoryIcon(category: string): string {
  switch (category) {
    case 'food': return 'restaurant';
    case 'plants': return 'leaf';
    case 'animals': return 'paw';
    case 'rocks': return 'diamond';
    case 'coins': return 'logo-bitcoin';
    default: return 'help-circle';
  }
}

function getPriorityColor(priority: string): string {
  switch (priority) {
    case 'high': return Colors.error;
    case 'medium': return Colors.warning;
    case 'low': return Colors.success;
    default: return Colors.textLight;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '600',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  actionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '600',
    textAlign: 'center',
  },
  scanItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  scanInfo: {
    flex: 1,
  },
  scanName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  scanCategory: {
    fontSize: 14,
    color: Colors.textSecondary,
    textTransform: 'capitalize',
  },
  scanConfidence: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.success,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  taskDue: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: 'center',
  },
});
