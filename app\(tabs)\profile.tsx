import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../src/constants/Colors';
import { useAppStore } from '../../src/store/useAppStore';
import { Card } from '../../src/components';

export default function ProfileScreen() {
  const { 
    userProfile, 
    scanHistory, 
    todoItems, 
    getScansByCategory,
    getCompletedTasksCount 
  } = useAppStore();

  const completedTasks = getCompletedTasksCount();
  const totalScans = scanHistory.length;

  const categoryStats = [
    { name: 'Food', count: getScansByCategory('food').length, icon: 'restaurant', color: Colors.food },
    { name: 'Plants', count: getScansByCategory('plants').length, icon: 'leaf', color: Colors.plants },
    { name: 'Animals', count: getScansByCategory('animals').length, icon: 'paw', color: Colors.animals },
    { name: 'Rocks', count: getScansByCategory('rocks').length, icon: 'diamond', color: Colors.rocks },
    { name: 'Coins', count: getScansByCategory('coins').length, icon: 'logo-bitcoin', color: Colors.coins },
  ];

  const achievements = [
    { title: 'First Scan', description: 'Completed your first scan', icon: 'camera', unlocked: totalScans >= 1 },
    { title: 'Explorer', description: 'Scanned 10 different items', icon: 'compass', unlocked: totalScans >= 10 },
    { title: 'Naturalist', description: 'Scanned 5 plants', icon: 'leaf', unlocked: getScansByCategory('plants').length >= 5 },
    { title: 'Foodie', description: 'Scanned 5 food items', icon: 'restaurant', unlocked: getScansByCategory('food').length >= 5 },
    { title: 'Task Master', description: 'Completed 10 tasks', icon: 'checkmark-circle', unlocked: completedTasks >= 10 },
  ];

  const menuItems = [
    { title: 'Settings', icon: 'settings-outline', onPress: () => Alert.alert('Settings', 'Settings screen coming soon!') },
    { title: 'Export Data', icon: 'download-outline', onPress: () => Alert.alert('Export', 'Data export coming soon!') },
    { title: 'Help & Support', icon: 'help-circle-outline', onPress: () => Alert.alert('Help', 'Help center coming soon!') },
    { title: 'About', icon: 'information-circle-outline', onPress: () => Alert.alert('About', 'BioScan v1.0.0\nAI-Powered Identification App') },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>

        {/* User Info */}
        <Card style={styles.userCard}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Ionicons name="person" size={32} color={Colors.primary} />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {userProfile?.name || 'BioScan User'}
              </Text>
              <Text style={styles.userEmail}>
                {userProfile?.email || '<EMAIL>'}
              </Text>
              <Text style={styles.joinDate}>
                Member since {userProfile?.createdAt ? 
                  new Date(userProfile.createdAt).toLocaleDateString() : 
                  new Date().toLocaleDateString()
                }
              </Text>
            </View>
            <TouchableOpacity style={styles.editButton}>
              <Ionicons name="pencil" size={20} color={Colors.primary} />
            </TouchableOpacity>
          </View>
        </Card>

        {/* Stats Overview */}
        <Card style={styles.statsCard}>
          <Text style={styles.sectionTitle}>Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{totalScans}</Text>
              <Text style={styles.statLabel}>Total Scans</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{completedTasks}</Text>
              <Text style={styles.statLabel}>Tasks Done</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{achievements.filter(a => a.unlocked).length}</Text>
              <Text style={styles.statLabel}>Achievements</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{categoryStats.filter(c => c.count > 0).length}</Text>
              <Text style={styles.statLabel}>Categories</Text>
            </View>
          </View>
        </Card>

        {/* Category Breakdown */}
        <Card style={styles.categoryCard}>
          <Text style={styles.sectionTitle}>Scan Categories</Text>
          {categoryStats.map((category, index) => (
            <View key={index} style={styles.categoryItem}>
              <View style={styles.categoryInfo}>
                <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
                  <Ionicons name={category.icon as any} size={20} color={category.color} />
                </View>
                <Text style={styles.categoryName}>{category.name}</Text>
              </View>
              <Text style={styles.categoryCount}>{category.count}</Text>
            </View>
          ))}
        </Card>

        {/* Achievements */}
        <Card style={styles.achievementsCard}>
          <Text style={styles.sectionTitle}>Achievements</Text>
          {achievements.map((achievement, index) => (
            <View key={index} style={[
              styles.achievementItem,
              !achievement.unlocked && styles.achievementLocked
            ]}>
              <View style={[
                styles.achievementIcon,
                { backgroundColor: achievement.unlocked ? Colors.success + '20' : Colors.textLight + '20' }
              ]}>
                <Ionicons 
                  name={achievement.icon as any} 
                  size={20} 
                  color={achievement.unlocked ? Colors.success : Colors.textLight} 
                />
              </View>
              <View style={styles.achievementInfo}>
                <Text style={[
                  styles.achievementTitle,
                  !achievement.unlocked && styles.achievementTitleLocked
                ]}>
                  {achievement.title}
                </Text>
                <Text style={styles.achievementDescription}>
                  {achievement.description}
                </Text>
              </View>
              {achievement.unlocked && (
                <Ionicons name="checkmark-circle" size={24} color={Colors.success} />
              )}
            </View>
          ))}
        </Card>

        {/* Menu Items */}
        <Card style={styles.menuCard}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.menuItem,
                index < menuItems.length - 1 && styles.menuItemBorder
              ]}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                <Ionicons name={item.icon as any} size={24} color={Colors.textSecondary} />
                <Text style={styles.menuItemText}>{item.title}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
            </TouchableOpacity>
          ))}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
  },
  userCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  joinDate: {
    fontSize: 12,
    color: Colors.textLight,
  },
  editButton: {
    padding: 8,
  },
  statsCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  categoryCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    color: Colors.text,
  },
  categoryCount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  achievementsCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  achievementLocked: {
    opacity: 0.5,
  },
  achievementIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  achievementTitleLocked: {
    color: Colors.textSecondary,
  },
  achievementDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  menuCard: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  menuItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
});
