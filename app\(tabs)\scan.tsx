import { Ionicons } from '@expo/vector-icons';
import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import React, { useRef, useState } from 'react';
import {
    Alert,
    Dimensions,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { Button } from '../../src/components';
import { Colors } from '../../src/constants/Colors';
import { CameraService, CapturedImage } from '../../src/services/CameraService';
import { GeminiService } from '../../src/services/GeminiService';
import { useAppStore } from '../../src/store/useAppStore';
import { ScanHistory } from '../../src/types';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

export default function ScanScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCapturing, setIsCapturing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const cameraRef = useRef<CameraView>(null!);

  const { addScanToHistory } = useAppStore();
  const router = useRouter();

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.container}>
        <Text style={styles.message}>Loading camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-outline" size={64} color={Colors.textLight} />
          <Text style={styles.message}>We need your permission to show the camera</Text>
          <Button
            title="Grant Permission"
            onPress={requestPermission}
            style={styles.permissionButton}
          />
        </View>
      </View>
    );
  }

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    try {
      setIsCapturing(true);

      const capturedImage = await CameraService.takePicture(cameraRef, {
        quality: 0.8,
        base64: false,
      });

      if (capturedImage) {
        await processImage(capturedImage);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to capture image');
    } finally {
      setIsCapturing(false);
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const result = await CameraService.pickImageFromGallery({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (result && !Array.isArray(result)) {
        await processImage(result);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const processImage = async (image: CapturedImage) => {
    try {
      setIsProcessing(true);

      // Prepare image for API
      const preparedImage = await CameraService.prepareImageForAPI(image);

      if (preparedImage) {
        // Send to Gemini API for identification
        const identificationResult = await GeminiService.identifyItem(preparedImage.uri);

        if (identificationResult) {
          // Create scan history entry
          const scanHistoryEntry: ScanHistory = {
            id: identificationResult.id,
            result: identificationResult,
            originalImage: image.uri,
            isFavorite: false,
          };

          // Add to scan history
          addScanToHistory(scanHistoryEntry);

          // Show success message
          Alert.alert(
            'Item Identified!',
            `Found: ${identificationResult.name}\nCategory: ${identificationResult.category}\nConfidence: ${Math.round(identificationResult.confidence * 100)}%`,
            [
              { text: 'View Details', onPress: () => {/* TODO: Navigate to details */} },
              { text: 'OK' }
            ]
          );
        }
      }
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Processing Error', 'Failed to identify the item. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scan Item</Text>
        <Text style={styles.headerSubtitle}>Point camera at any item to identify</Text>
      </View>

      {/* Camera View */}
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
        >
          {/* Camera Overlay */}
          <View style={styles.overlay}>
            {/* Top Controls */}
            <View style={styles.topControls}>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={toggleCameraFacing}
              >
                <Ionicons name="camera-reverse" size={24} color={Colors.textInverse} />
              </TouchableOpacity>
            </View>

            {/* Center Frame */}
            <View style={styles.centerContainer}>
              <View style={styles.scanFrame}>
                <View style={[styles.corner, styles.topLeft]} />
                <View style={[styles.corner, styles.topRight]} />
                <View style={[styles.corner, styles.bottomLeft]} />
                <View style={[styles.corner, styles.bottomRight]} />
              </View>
              <Text style={styles.scanInstruction}>
                Position item within the frame
              </Text>
            </View>

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              <TouchableOpacity
                style={styles.galleryButton}
                onPress={pickImageFromGallery}
              >
                <Ionicons name="images" size={24} color={Colors.textInverse} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.captureButton, (isCapturing || isProcessing) && styles.capturingButton]}
                onPress={takePicture}
                disabled={isCapturing || isProcessing}
              >
                <View style={styles.captureButtonInner} />
              </TouchableOpacity>

              <TouchableOpacity style={styles.flashButton}>
                <Ionicons name="flash" size={24} color={Colors.textInverse} />
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      </View>

      {/* Quick Categories */}
      <View style={styles.categoriesContainer}>
        <Text style={styles.categoriesTitle}>Quick Categories</Text>
        <View style={styles.categories}>
          {[
            { name: 'Food', icon: 'restaurant', color: Colors.food },
            { name: 'Plants', icon: 'leaf', color: Colors.plants },
            { name: 'Animals', icon: 'paw', color: Colors.animals },
            { name: 'Rocks', icon: 'diamond', color: Colors.rocks },
            { name: 'Coins', icon: 'logo-bitcoin', color: Colors.coins },
          ].map((category) => (
            <TouchableOpacity
              key={category.name}
              style={[styles.categoryButton, { backgroundColor: category.color + '20' }]}
            >
              <Ionicons name={category.icon as any} size={20} color={category.color} />
              <Text style={[styles.categoryText, { color: category.color }]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    fontSize: 18,
    color: Colors.text,
    textAlign: 'center',
    marginVertical: 16,
  },
  permissionButton: {
    marginTop: 16,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 20,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.overlay,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: Colors.textInverse,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scanInstruction: {
    color: Colors.textInverse,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 20,
    backgroundColor: Colors.overlay,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 30,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.overlay,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.textInverse,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 4,
    borderColor: Colors.overlay,
  },
  capturingButton: {
    opacity: 0.7,
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary,
  },
  flashButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.overlay,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoriesContainer: {
    padding: 20,
    paddingTop: 10,
  },
  categoriesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  categories: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryButton: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});
