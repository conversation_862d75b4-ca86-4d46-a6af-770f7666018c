import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Modal,
  Alert,
  ViewStyle, // Import ViewStyle from react-native
  StyleProp, // Import StyleProp for more flexible style typing
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../src/constants/Colors';
import { useAppStore } from '../../src/store/useAppStore';
import { Button, Card, Input } from '../../src/components';
import { TodoItem } from '../../src/types'; // Assuming TodoItem.dueDate is `Date | undefined`
import DateTimePickerModal from 'react-native-modal-datetime-picker';

export default function TodoScreen() {
  const {
    todoItems,
    addTodoItem,
    updateTodoItem,
    deleteTodoItem,
    toggleTodoComplete
  } = useAppStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [newTaskPriority, setNewTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [filter, setFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDueDate, setSelectedDueDate] = useState<Date | null>(null);

  const filteredTodos = todoItems.filter(item => {
    switch (filter) {
      case 'pending': return !item.completed;
      case 'completed': return item.completed;
      default: return true;
    }
  });

  const pendingCount = todoItems.filter(item => !item.completed).length;
  const completedCount = todoItems.filter(item => item.completed).length;

  const addTask = () => {
    if (!newTaskTitle.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const newTask: TodoItem = {
      id: Date.now().toString(),
      title: newTaskTitle.trim(),
      description: newTaskDescription.trim() || undefined,
      completed: false,
      priority: newTaskPriority,
      category: 'general',
      createdAt: new Date(),
      updatedAt: new Date(),
      dueDate: selectedDueDate || undefined, // selectedDueDate is already Date | null
    };

    addTodoItem(newTask);
    setNewTaskTitle('');
    setNewTaskDescription('');
    setNewTaskPriority('medium');
    setSelectedDueDate(null);
    setShowAddModal(false);
  };

  const deleteTask = (taskId: string) => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteTodoItem(taskId) },
      ]
    );
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return Colors.error;
      case 'medium': return Colors.warning;
      case 'low': return Colors.success;
      default: return Colors.textLight;
    }
  };

  const getPriorityIcon = (priority: string): string => {
    switch (priority) {
      case 'high': return 'arrow-up';
      case 'medium': return 'remove';
      case 'low': return 'arrow-down';
      default: return 'remove';
    }
  };

  const renderTodoItem = ({ item }: { item: TodoItem }) => {
    // Filter out falsy values (like `false`) from the style array
    const cardStyles: StyleProp<ViewStyle> = [
      styles.todoCard,
      item.completed && styles.completedCard,
    ].filter(Boolean);

    return (
      <Card style={cardStyles}>
        <View style={styles.todoHeader}>
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => toggleTodoComplete(item.id)}
          >
            <Ionicons
              name={item.completed ? 'checkmark-circle' : 'ellipse-outline'}
              size={24}
              color={item.completed ? Colors.success : Colors.textSecondary}
            />
          </TouchableOpacity>

          <View style={styles.todoContent}>
            <Text style={[
              styles.todoTitle,
              item.completed && styles.completedText
            ]}>
              {item.title}
            </Text>

            {item.description && (
              <Text style={[
                styles.todoDescription,
                item.completed && styles.completedText
              ]}>
                {item.description}
              </Text>
            )}

            <View style={styles.todoMeta}>
              <View style={styles.priorityContainer}>
                <Ionicons
                  name={getPriorityIcon(item.priority) as any} // Still need 'as any' for Ionicons name prop
                  size={14}
                  color={getPriorityColor(item.priority)}
                />
                <Text style={[
                  styles.priorityText,
                  { color: getPriorityColor(item.priority) }
                ]}>
                  {item.priority}
                </Text>
              </View>

              {item.dueDate && (
                <Text style={styles.dueDateText}>
                  Due: {item.dueDate.toLocaleDateString()}
                </Text>
              )}
            </View>
          </View>

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteTask(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="checkmark-circle-outline" size={64} color={Colors.textLight} />
      <Text style={styles.emptyTitle}>
        {filter === 'completed' ? 'No completed tasks' : 'No tasks yet'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {filter === 'completed'
          ? 'Complete some tasks to see them here'
          : 'Add your first task to get started'
        }
      </Text>
    </View>
  );

  const handleConfirmDate = (date: Date) => {
    setSelectedDueDate(date);
    setShowDatePicker(false);
  };

  // FIX: Declare the filterTabs array as a separate constant with 'as const'
  const filterTabs = [
    { key: 'all', label: 'All', count: todoItems.length },
    { key: 'pending', label: 'Pending', count: pendingCount },
    { key: 'completed', label: 'Completed', count: completedCount },
  ] as const;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>To-Do List</Text>
        <Text style={styles.headerSubtitle}>
          {pendingCount} pending • {completedCount} completed
        </Text>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        {filterTabs.map((tab) => ( // Map over the declared filterTabs variable
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.filterTab,
              filter === tab.key && styles.filterTabActive
            ]}
            onPress={() => setFilter(tab.key)} // This is now valid!
          >
            <Text style={[
              styles.filterTabText,
              filter === tab.key && styles.filterTabTextActive
            ]}>
              {tab.label} ({tab.count})
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Todo List */}
      <FlatList
        data={filteredTodos}
        keyExtractor={(item) => item.id}
        renderItem={renderTodoItem}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Add Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setShowAddModal(true)}
      >
        <Ionicons name="add" size={24} color={Colors.textInverse} />
      </TouchableOpacity>

      {/* Add Task Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Task</Text>
            <TouchableOpacity onPress={addTask}>
              <Text style={styles.saveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Input
              label="Task Title"
              placeholder="Enter task title..."
              value={newTaskTitle}
              onChangeText={setNewTaskTitle}
            />

            <Input
              label="Description (Optional)"
              placeholder="Enter task description..."
              value={newTaskDescription}
              onChangeText={setNewTaskDescription} // Corrected: was setNewTaskTitle
              multiline
              numberOfLines={3}
            />

            <Text style={styles.priorityLabel}>Priority</Text>
            <View style={styles.priorityOptions}>
              {(['low', 'medium', 'high'] as const).map((priority) => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityOption,
                    newTaskPriority === priority && styles.priorityOptionActive,
                    { borderColor: getPriorityColor(priority) }
                  ]}
                  onPress={() => setNewTaskPriority(priority)}
                >
                  <Ionicons
                    name={getPriorityIcon(priority) as any}
                    size={16}
                    color={getPriorityColor(priority)}
                  />
                  <Text style={[
                    styles.priorityOptionText,
                    { color: getPriorityColor(priority) }
                  ]}>
                    {priority.charAt(0).toUpperCase() + priority.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.datePickerText}>
                {selectedDueDate ? selectedDueDate.toLocaleDateString() : 'Select Due Date'}
              </Text>
            </TouchableOpacity>

            <DateTimePickerModal
              isVisible={showDatePicker}
              mode="date"
              onConfirm={handleConfirmDate}
              onCancel={() => setShowDatePicker(false)}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: Colors.backgroundSecondary,
    alignItems: 'center',
  },
  filterTabActive: {
    backgroundColor: Colors.primary,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  filterTabTextActive: {
    color: Colors.textInverse,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  todoCard: {
    marginBottom: 12,
  },
  completedCard: {
    opacity: 0.7,
  },
  todoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkbox: {
    marginRight: 12,
    marginTop: 2,
  },
  todoContent: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  todoDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  completedText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'uppercase',
  },
  dueDateText: {
    fontSize: 12,
    color: Colors.textLight,
  },
  deleteButton: {
    padding: 4,
    marginLeft: 8,
  },
  addButton: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  cancelText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  priorityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  priorityOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 12,
    borderWidth: 2,
    backgroundColor: Colors.backgroundSecondary,
  },
  priorityOptionActive: {
    backgroundColor: Colors.background,
  },
  priorityOptionText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  datePickerButton: {
    marginTop: 20,
    padding: 15,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 8,
    alignItems: 'center',
  },
  datePickerText: {
    fontSize: 16,
    color: Colors.text,
  },
});
