import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { <PERSON><PERSON>, Card } from '../src/components';
import { HealthDataVisualization } from '../src/components/HealthDataVisualization';
import { Colors } from '../src/constants/Colors';
import { HealthKitService } from '../src/services/HealthKitService';

export default function HealthScreen() {
  const router = useRouter();
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [platform, setPlatform] = useState('');
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    end: new Date(),
  });

  useEffect(() => {
    checkHealthKitStatus();
  }, []);

  const checkHealthKitStatus = async () => {
    try {
      const status = HealthKitService.getConnectionStatus();
      setIsConnected(status.connected);
      setPlatform(status.platform);
    } catch (error) {
      console.error('Error checking health kit status:', error);
    }
  };

  const handleConnectHealthKit = async () => {
    try {
      setIsLoading(true);

      if (!HealthKitService.isAvailable()) {
        Alert.alert(
          'Not Available',
          'Health integration is not available on this platform.'
        );
        return;
      }

      // Initialize health kit
      const initialized = await HealthKitService.initialize();
      if (!initialized) {
        Alert.alert(
          'Initialization Failed',
          'Failed to initialize health integration. Please try again.'
        );
        return;
      }

      // Request permissions
      const permissionsGranted = await HealthKitService.requestPermissions();
      if (!permissionsGranted) {
        Alert.alert(
          'Permissions Required',
          `Please grant permissions to access ${platform === 'ios' ? 'Apple Health' : 'Google Fit'} data in your device settings.`
        );
        return;
      }

      setIsConnected(true);
      Alert.alert(
        'Connected Successfully',
        `Your nutrition data will now be synced with ${platform === 'ios' ? 'Apple Health' : 'Google Fit'}.`
      );
    } catch (error) {
      console.error('Error connecting to health kit:', error);
      Alert.alert(
        'Connection Failed',
        'Failed to connect to health platform. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnectHealthKit = async () => {
    try {
      Alert.alert(
        'Disconnect Health Integration',
        `Are you sure you want to disconnect from ${platform === 'ios' ? 'Apple Health' : 'Google Fit'}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Disconnect',
            style: 'destructive',
            onPress: async () => {
              await HealthKitService.disconnect();
              setIsConnected(false);
              Alert.alert(
                'Disconnected',
                'Health integration has been disconnected.'
              );
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error disconnecting from health kit:', error);
    }
  };

  const changeDateRange = (days: number) => {
    const end = new Date();
    const start = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    setDateRange({ start, end });
  };

  const testHealthIntegration = async () => {
    try {
      // Test writing a sample nutrition entry
      const testEntry = {
        id: 'test_entry',
        foodItem: 'Test Apple',
        quantity: 1,
        unit: 'medium',
        timestamp: new Date().toISOString(),
        mealType: 'snack' as const,
        nutrients: {
          calories: 95,
          carbs: 25,
          fiber: 4,
          sugar: 19,
          protein: 0.5,
          fat: 0.3,
        },
        source: 'test',
      };

      await HealthKitService.writeNutritionData(testEntry);
      Alert.alert(
        'Test Successful',
        'Test nutrition data has been written to your health app. Check your health app to verify the integration is working.'
      );
    } catch (error) {
      console.error('Health integration test failed:', error);
      Alert.alert(
        'Test Failed',
        'Failed to write test data to health app. Please check your permissions and try again.'
      );
    }
  };

  const renderConnectionCard = () => {
    const platformName = platform === 'ios' ? 'Apple Health' : 'Google Fit';
    const platformIcon = platform === 'ios' ? 'heart' : 'fitness';

    return (
      <Card style={styles.connectionCard}>
        <View style={styles.connectionHeader}>
          <View style={styles.platformInfo}>
            <Ionicons name={platformIcon as any} size={32} color={Colors.primary} />
            <View style={styles.platformDetails}>
              <Text style={styles.platformName}>{platformName}</Text>
              <Text style={[
                styles.connectionStatus,
                { color: isConnected ? Colors.success : Colors.textSecondary }
              ]}>
                {isConnected ? 'Connected' : 'Not Connected'}
              </Text>
            </View>
          </View>
          
          {isConnected ? (
            <TouchableOpacity
              style={styles.disconnectButton}
              onPress={handleDisconnectHealthKit}
            >
              <Ionicons name="unlink" size={20} color={Colors.error} />
            </TouchableOpacity>
          ) : null}
        </View>

        {!isConnected && (
          <View style={styles.connectionContent}>
            <Text style={styles.connectionDescription}>
              Connect to {platformName} to automatically sync your nutrition data and view comprehensive health insights.
            </Text>
            
            <View style={styles.benefitsList}>
              <View style={styles.benefitItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.benefitText}>Automatic nutrition logging</Text>
              </View>
              <View style={styles.benefitItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.benefitText}>Comprehensive health metrics</Text>
              </View>
              <View style={styles.benefitItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.benefitText}>Activity and calorie tracking</Text>
              </View>
            </View>

            <Button
              title={`Connect to ${platformName}`}
              onPress={handleConnectHealthKit}
              loading={isLoading}
              style={styles.connectButton}
            />

            <Button
              title="Test Health Integration"
              onPress={testHealthIntegration}
              variant="outline"
              style={styles.testButton}
            />
          </View>
        )}
      </Card>
    );
  };

  const renderDateRangeSelector = () => {
    const ranges = [
      { label: 'Today', days: 1 },
      { label: '7 Days', days: 7 },
      { label: '30 Days', days: 30 },
      { label: '90 Days', days: 90 },
    ];

    const currentDays = Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (24 * 60 * 60 * 1000));

    return (
      <Card style={styles.dateRangeCard}>
        <Text style={styles.dateRangeTitle}>Time Period</Text>
        <View style={styles.dateRangeOptions}>
          {ranges.map((range) => (
            <TouchableOpacity
              key={range.days}
              style={[
                styles.dateRangeOption,
                currentDays === range.days && styles.dateRangeOptionActive,
              ]}
              onPress={() => changeDateRange(range.days)}
            >
              <Text style={[
                styles.dateRangeOptionText,
                currentDays === range.days && styles.dateRangeOptionTextActive,
              ]}>
                {range.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Health & Nutrition</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderConnectionCard()}
        
        {isConnected && (
          <>
            {renderDateRangeSelector()}
            <HealthDataVisualization dateRange={dateRange} />
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  connectionCard: {
    marginBottom: 20,
  },
  connectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  platformInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  platformDetails: {
    marginLeft: 16,
  },
  platformName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  connectionStatus: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 2,
  },
  disconnectButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.error + '20',
  },
  connectionContent: {
    marginTop: 8,
  },
  connectionDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  benefitsList: {
    marginBottom: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
  },
  connectButton: {
    marginTop: 8,
  },
  testButton: {
    marginTop: 8,
  },
  dateRangeCard: {
    marginBottom: 20,
  },
  dateRangeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  dateRangeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateRangeOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 2,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  dateRangeOptionActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '20',
  },
  dateRangeOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  dateRangeOptionTextActive: {
    color: Colors.primary,
  },
});
