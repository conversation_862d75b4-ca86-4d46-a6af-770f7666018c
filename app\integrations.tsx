import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../src/constants/Colors';
import { Card, Button } from '../src/components';
import { VoiceAssistantService } from '../src/services/VoiceAssistantService';
import { GoogleFitService } from '../src/services/GoogleFitService';
import { ThirdPartyIntegrationService } from '../src/services/ThirdPartyIntegrationService';
import { ThirdPartyApp } from '../src/types';

export default function IntegrationsScreen() {
  const router = useRouter();
  const [voiceIntegrationEnabled, setVoiceIntegrationEnabled] = useState(false);
  const [googleFitConnected, setGoogleFitConnected] = useState(false);
  const [connectedApps, setConnectedApps] = useState<ThirdPartyApp[]>([]);
  const [supportedApps, setSupportedApps] = useState<ThirdPartyApp[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadIntegrationStatus();
  }, []);

  const loadIntegrationStatus = async () => {
    try {
      // Load voice integration status
      const voiceStatus = VoiceAssistantService.getVoiceIntegrationStatus();
      setVoiceIntegrationEnabled(voiceStatus.available);

      // Load Google Fit status
      const googleFitStatus = GoogleFitService.getConnectionStatus();
      setGoogleFitConnected(googleFitStatus.connected);

      // Load third-party apps
      await ThirdPartyIntegrationService.initialize();
      const connected = ThirdPartyIntegrationService.getConnectedApps();
      const supported = ThirdPartyIntegrationService.getSupportedApps();
      
      setConnectedApps(connected);
      setSupportedApps(supported);
    } catch (error) {
      console.error('Error loading integration status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoiceIntegrationToggle = async (enabled: boolean) => {
    try {
      if (enabled) {
        const success = await VoiceAssistantService.enableVoiceIntegration();
        if (success) {
          setVoiceIntegrationEnabled(true);
          Alert.alert(
            'Voice Integration Enabled',
            'You can now use voice commands like "Hey Siri, scan with BioScan" or "Hey Google, add task to BioScan"'
          );
        } else {
          Alert.alert('Error', 'Failed to enable voice integration');
        }
      } else {
        setVoiceIntegrationEnabled(false);
        Alert.alert('Voice Integration Disabled', 'Voice commands are no longer available');
      }
    } catch (error) {
      console.error('Error toggling voice integration:', error);
      Alert.alert('Error', 'Failed to toggle voice integration');
    }
  };

  const handleGoogleFitToggle = async (enabled: boolean) => {
    try {
      if (enabled) {
        const success = await GoogleFitService.connect();
        if (success) {
          setGoogleFitConnected(true);
          Alert.alert(
            'Google Fit Connected',
            'Your food scans will now be automatically logged to Google Fit as nutrition data'
          );
        } else {
          Alert.alert('Error', 'Failed to connect to Google Fit');
        }
      } else {
        await GoogleFitService.disconnect();
        setGoogleFitConnected(false);
        Alert.alert('Google Fit Disconnected', 'Nutrition data will no longer be synced');
      }
    } catch (error) {
      console.error('Error toggling Google Fit:', error);
      Alert.alert('Error', 'Failed to toggle Google Fit connection');
    }
  };

  const handleAppConnection = async (app: ThirdPartyApp) => {
    try {
      const isConnected = ThirdPartyIntegrationService.isAppConnected(app.id);
      
      if (isConnected) {
        Alert.alert(
          'Disconnect App',
          `Are you sure you want to disconnect ${app.name}?`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Disconnect',
              style: 'destructive',
              onPress: async () => {
                await ThirdPartyIntegrationService.disconnectApp(app.id);
                await loadIntegrationStatus();
              },
            },
          ]
        );
      } else {
        const success = await ThirdPartyIntegrationService.connectApp(app.id);
        if (success) {
          Alert.alert(
            'App Connected',
            `${app.name} has been connected successfully. Your tasks will now be synced automatically.`
          );
          await loadIntegrationStatus();
        } else {
          Alert.alert('Error', `Failed to connect to ${app.name}`);
        }
      }
    } catch (error) {
      console.error('Error handling app connection:', error);
      Alert.alert('Error', 'Failed to update app connection');
    }
  };

  const renderThirdPartyApp = (app: ThirdPartyApp) => {
    const isConnected = connectedApps.some(connectedApp => connectedApp.id === app.id);
    
    return (
      <TouchableOpacity
        key={app.id}
        style={styles.appItem}
        onPress={() => handleAppConnection(app)}
      >
        <View style={styles.appInfo}>
          <View style={styles.appIcon}>
            <Ionicons name={app.icon as any} size={24} color={Colors.primary} />
          </View>
          <View style={styles.appDetails}>
            <Text style={styles.appName}>{app.name}</Text>
            <Text style={styles.appDescription}>{app.description}</Text>
            <View style={styles.appFeatures}>
              {app.features.slice(0, 3).map((feature, index) => (
                <Text key={index} style={styles.featureTag}>
                  {feature}
                </Text>
              ))}
            </View>
          </View>
        </View>
        <View style={styles.connectionStatus}>
          <Text style={[
            styles.statusText,
            { color: isConnected ? Colors.success : Colors.textSecondary }
          ]}>
            {isConnected ? 'Connected' : 'Connect'}
          </Text>
          <Ionicons
            name={isConnected ? 'checkmark-circle' : 'add-circle-outline'}
            size={20}
            color={isConnected ? Colors.success : Colors.textSecondary}
          />
        </View>
      </TouchableOpacity>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loading}>
          <Text style={styles.loadingText}>Loading integrations...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Integrations</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Voice Assistant Integration */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Voice Assistant</Text>
          
          <View style={styles.integrationItem}>
            <View style={styles.integrationInfo}>
              <View style={styles.integrationHeader}>
                <Ionicons name="mic" size={24} color={Colors.primary} />
                <Text style={styles.integrationName}>Voice Commands</Text>
              </View>
              <Text style={styles.integrationDescription}>
                Use "Hey Siri" or "Hey Google" to control BioScan with voice commands
              </Text>
              <View style={styles.voiceCommands}>
                <Text style={styles.commandExample}>"Hey Siri, scan with BioScan"</Text>
                <Text style={styles.commandExample}>"Hey Google, add task to BioScan"</Text>
              </View>
            </View>
            <Switch
              value={voiceIntegrationEnabled}
              onValueChange={handleVoiceIntegrationToggle}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={voiceIntegrationEnabled ? Colors.primary : Colors.textLight}
            />
          </View>
        </Card>

        {/* Google Fit Integration */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Health & Fitness</Text>
          
          <View style={styles.integrationItem}>
            <View style={styles.integrationInfo}>
              <View style={styles.integrationHeader}>
                <Ionicons name="fitness" size={24} color={Colors.accent} />
                <Text style={styles.integrationName}>Google Fit</Text>
              </View>
              <Text style={styles.integrationDescription}>
                Automatically log food scans as nutrition data in Google Fit
              </Text>
              {googleFitConnected && (
                <Text style={styles.connectedNote}>
                  ✓ Food scans are being logged to Google Fit
                </Text>
              )}
            </View>
            <Switch
              value={googleFitConnected}
              onValueChange={handleGoogleFitToggle}
              trackColor={{ false: Colors.border, true: Colors.accent + '50' }}
              thumbColor={googleFitConnected ? Colors.accent : Colors.textLight}
            />
          </View>
        </Card>

        {/* Third-Party Apps */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Productivity Apps</Text>
          <Text style={styles.sectionSubtitle}>
            Connect your favorite apps to automatically sync tasks and data
          </Text>
          
          {supportedApps
            .filter(app => app.category === 'productivity')
            .map(renderThirdPartyApp)}
        </Card>

        {/* Communication Apps */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Communication</Text>
          
          {supportedApps
            .filter(app => app.category === 'communication')
            .map(renderThirdPartyApp)}
        </Card>

        {/* Notes Apps */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Notes & Documentation</Text>
          
          {supportedApps
            .filter(app => app.category === 'notes')
            .map(renderThirdPartyApp)}
        </Card>

        {/* Integration Tips */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Integration Tips</Text>
          
          <View style={styles.tipsList}>
            <View style={styles.tip}>
              <Ionicons name="bulb" size={16} color={Colors.warning} />
              <Text style={styles.tipText}>
                Enable voice commands to quickly scan items hands-free
              </Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="bulb" size={16} color={Colors.warning} />
              <Text style={styles.tipText}>
                Connect Google Fit to track your nutrition automatically
              </Text>
            </View>
            <View style={styles.tip}>
              <Ionicons name="bulb" size={16} color={Colors.warning} />
              <Text style={styles.tipText}>
                Link productivity apps to keep all your tasks in sync
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 16,
  },
  integrationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  integrationInfo: {
    flex: 1,
    marginRight: 16,
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  integrationName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 12,
  },
  integrationDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  connectedNote: {
    fontSize: 12,
    color: Colors.success,
    fontWeight: '600',
  },
  voiceCommands: {
    marginTop: 8,
  },
  commandExample: {
    fontSize: 12,
    color: Colors.textLight,
    fontStyle: 'italic',
    marginBottom: 2,
  },
  appItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  appInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  appIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: Colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  appDetails: {
    flex: 1,
  },
  appName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  appDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  appFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureTag: {
    fontSize: 10,
    color: Colors.primary,
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 2,
  },
  connectionStatus: {
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  tipsList: {
    marginTop: 8,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
});
