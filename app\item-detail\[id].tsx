import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors, getColorByCategory } from '../../src/constants/Colors';
import { useAppStore } from '../../src/store/useAppStore';
import { Card } from '../../src/components';

const { width } = Dimensions.get('window');

export default function ItemDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { scanHistory, updateScanInHistory } = useAppStore();
  
  const scanItem = scanHistory.find(item => item.id === id);
  
  if (!scanItem) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={Colors.error} />
          <Text style={styles.errorText}>Item not found</Text>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const { result } = scanItem;
  const categoryColor = getColorByCategory(result.category);

  const toggleFavorite = () => {
    updateScanInHistory(scanItem.id, { isFavorite: !scanItem.isFavorite });
  };

  const renderCategorySpecificInfo = () => {
    const additionalInfo = result.additionalInfo;
    if (!additionalInfo) return null;

    switch (result.category) {
      case 'food':
        return (
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Nutritional Information</Text>
            {additionalInfo.nutrition && (
              <View style={styles.nutritionGrid}>
                {Object.entries(additionalInfo.nutrition).map(([key, value]) => (
                  <View key={key} style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
                    <Text style={styles.nutritionValue}>{value}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        );

      case 'plants':
        return (
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Care Instructions</Text>
            {additionalInfo.care && (
              <View style={styles.careInfo}>
                {Object.entries(additionalInfo.care).map(([key, value]) => (
                  <View key={key} style={styles.careItem}>
                    <Text style={styles.careLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}:</Text>
                    <Text style={styles.careValue}>{value}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        );

      case 'animals':
        return (
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Habitat & Behavior</Text>
            {additionalInfo.habitat && (
              <View style={styles.habitatInfo}>
                {Object.entries(additionalInfo.habitat).map(([key, value]) => (
                  <View key={key} style={styles.habitatItem}>
                    <Text style={styles.habitatLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}:</Text>
                    <Text style={styles.habitatValue}>{value}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        );

      case 'rocks':
        return (
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Properties</Text>
            {additionalInfo.properties && (
              <View style={styles.propertiesInfo}>
                {Object.entries(additionalInfo.properties).map(([key, value]) => (
                  <View key={key} style={styles.propertyItem}>
                    <Text style={styles.propertyLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}:</Text>
                    <Text style={styles.propertyValue}>{value}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        );

      case 'coins':
        return (
          <Card style={styles.infoCard}>
            <Text style={styles.infoTitle}>Value & Information</Text>
            {additionalInfo.value && (
              <View style={styles.valueInfo}>
                {Object.entries(additionalInfo.value).map(([key, value]) => (
                  <View key={key} style={styles.valueItem}>
                    <Text style={styles.valueLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}:</Text>
                    <Text style={styles.valueValue}>{value}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Item Details</Text>
        <TouchableOpacity onPress={toggleFavorite} style={styles.favoriteButton}>
          <Ionicons
            name={scanItem.isFavorite ? 'heart' : 'heart-outline'}
            size={24}
            color={scanItem.isFavorite ? Colors.error : Colors.textSecondary}
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image */}
        {result.image && (
          <View style={styles.imageContainer}>
            <Image source={{ uri: result.image }} style={styles.image} />
            <View style={[styles.categoryBadge, { backgroundColor: categoryColor }]}>
              <Text style={styles.categoryBadgeText}>{result.category}</Text>
            </View>
          </View>
        )}

        {/* Basic Info */}
        <Card style={styles.basicInfoCard}>
          <Text style={styles.itemName}>{result.name}</Text>
          {result.scientificName && (
            <Text style={styles.scientificName}>{result.scientificName}</Text>
          )}
          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceLabel}>Confidence:</Text>
            <Text style={[styles.confidenceValue, { color: categoryColor }]}>
              {Math.round(result.confidence * 100)}%
            </Text>
          </View>
          <Text style={styles.description}>{result.description}</Text>
        </Card>

        {/* Category-specific information */}
        {renderCategorySpecificInfo()}

        {/* Fun Facts */}
        {result.additionalInfo?.facts && (
          <Card style={styles.factsCard}>
            <Text style={styles.factsTitle}>Did You Know?</Text>
            {result.additionalInfo.facts.map((fact: string, index: number) => (
              <View key={index} style={styles.factItem}>
                <Ionicons name="bulb" size={16} color={categoryColor} />
                <Text style={styles.factText}>{fact}</Text>
              </View>
            ))}
          </Card>
        )}

        {/* Alternative Identifications */}
        {result.additionalInfo?.alternatives && result.additionalInfo.alternatives.length > 0 && (
          <Card style={styles.alternativesCard}>
            <Text style={styles.alternativesTitle}>Alternative Identifications</Text>
            {result.additionalInfo.alternatives.map((alt: any, index: number) => (
              <View key={index} style={styles.alternativeItem}>
                <Text style={styles.alternativeName}>{alt.name}</Text>
                <Text style={styles.alternativeConfidence}>
                  {Math.round(alt.confidence * 100)}%
                </Text>
              </View>
            ))}
          </Card>
        )}

        {/* Scan Info */}
        <Card style={styles.scanInfoCard}>
          <Text style={styles.scanInfoTitle}>Scan Information</Text>
          <View style={styles.scanInfoItem}>
            <Text style={styles.scanInfoLabel}>Scanned on:</Text>
            <Text style={styles.scanInfoValue}>
              {new Date(result.timestamp).toLocaleDateString()} at{' '}
              {new Date(result.timestamp).toLocaleTimeString()}
            </Text>
          </View>
          {result.location && (
            <View style={styles.scanInfoItem}>
              <Text style={styles.scanInfoLabel}>Location:</Text>
              <Text style={styles.scanInfoValue}>
                {result.location.address || `${result.location.latitude}, ${result.location.longitude}`}
              </Text>
            </View>
          )}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  favoriteButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 250,
    borderRadius: 16,
  },
  categoryBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  categoryBadgeText: {
    color: Colors.textInverse,
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  basicInfoCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  itemName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: Colors.textSecondary,
    marginBottom: 12,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  confidenceLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginRight: 8,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  infoCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    width: '48%',
    marginBottom: 8,
  },
  nutritionLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  careInfo: {},
  careItem: {
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  careValue: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 2,
  },
  habitatInfo: {},
  habitatItem: {
    marginBottom: 8,
  },
  habitatLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  habitatValue: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 2,
  },
  propertiesInfo: {},
  propertyItem: {
    marginBottom: 8,
  },
  propertyLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  propertyValue: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 2,
  },
  valueInfo: {},
  valueItem: {
    marginBottom: 8,
  },
  valueLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  valueValue: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 2,
  },
  factsCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  factsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  factItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  factText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 8,
    flex: 1,
    lineHeight: 22,
  },
  alternativesCard: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  alternativesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  alternativeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  alternativeName: {
    fontSize: 16,
    color: Colors.text,
  },
  alternativeConfidence: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  scanInfoCard: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  scanInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  scanInfoItem: {
    marginBottom: 8,
  },
  scanInfoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  scanInfoValue: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.error,
    marginTop: 16,
    marginBottom: 16,
  },
  backText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '600',
  },
});
