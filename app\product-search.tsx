import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../src/constants/Colors';
import { Card, Button } from '../src/components';
import { ProductResearchService } from '../src/services/ProductResearchService';
import { ProductSearchResult, ProductInfo, PriceComparison, ProductReview } from '../src/types';

export default function ProductSearchScreen() {
  const router = useRouter();
  const { query } = useLocalSearchParams<{ query: string }>();
  
  const [searchResults, setSearchResults] = useState<ProductSearchResult[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<ProductInfo | null>(null);
  const [priceComparison, setPriceComparison] = useState<PriceComparison[]>([]);
  const [reviews, setReviews] = useState<ProductReview[]>([]);
  const [recommendations, setRecommendations] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'results' | 'details' | 'prices' | 'reviews'>('results');

  useEffect(() => {
    if (query) {
      searchProducts(query);
    }
  }, [query]);

  const searchProducts = async (searchQuery: string) => {
    try {
      setIsLoading(true);
      const results = await ProductResearchService.searchProducts(searchQuery);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching products:', error);
      Alert.alert('Search Error', 'Failed to search products. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const selectProduct = async (product: ProductSearchResult) => {
    try {
      setIsLoading(true);
      setActiveTab('details');
      
      const [productInfo, prices, productReviews, buyingRecs] = await Promise.all([
        ProductResearchService.getProductDetails(product.id),
        ProductResearchService.getPriceComparison(product.id),
        ProductResearchService.getProductReviews(product.id),
        ProductResearchService.getBuyingRecommendations(product.id),
      ]);

      setSelectedProduct(productInfo);
      setPriceComparison(prices);
      setReviews(productReviews);
      setRecommendations(buyingRecs);
    } catch (error) {
      console.error('Error loading product details:', error);
      Alert.alert('Error', 'Failed to load product details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const openProductUrl = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Cannot open this link');
      }
    } catch (error) {
      console.error('Error opening URL:', error);
      Alert.alert('Error', 'Failed to open link');
    }
  };

  const renderSearchResults = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Searching products...</Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.resultsContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.resultsHeader}>
          Found {searchResults.length} products for "{query}"
        </Text>
        
        {searchResults.map((product) => (
          <TouchableOpacity
            key={product.id}
            style={styles.productCard}
            onPress={() => selectProduct(product)}
          >
            <Image source={{ uri: product.image }} style={styles.productImage} />
            <View style={styles.productInfo}>
              <Text style={styles.productName} numberOfLines={2}>
                {product.name}
              </Text>
              <Text style={styles.productDescription} numberOfLines={2}>
                {product.description}
              </Text>
              <View style={styles.productMeta}>
                <Text style={styles.productPrice}>
                  ${product.price} {product.currency}
                </Text>
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={14} color={Colors.warning} />
                  <Text style={styles.ratingText}>
                    {product.rating} ({product.reviewCount})
                  </Text>
                </View>
              </View>
              <Text style={styles.retailerText}>
                Available at {product.retailer}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderProductDetails = () => {
    if (!selectedProduct) return null;

    return (
      <ScrollView style={styles.detailsContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.productHeader}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.imageGallery}>
              {selectedProduct.images.map((image, index) => (
                <Image key={index} source={{ uri: image }} style={styles.galleryImage} />
              ))}
            </View>
          </ScrollView>
          
          <Text style={styles.productTitle}>{selectedProduct.name}</Text>
          <Text style={styles.productBrand}>{selectedProduct.brand} • {selectedProduct.model}</Text>
          <Text style={styles.productDescription}>{selectedProduct.description}</Text>
        </View>

        <Card style={styles.detailCard}>
          <Text style={styles.sectionTitle}>Specifications</Text>
          {Object.entries(selectedProduct.specifications).map(([key, value]) => (
            <View key={key} style={styles.specRow}>
              <Text style={styles.specKey}>{key}</Text>
              <Text style={styles.specValue}>{value}</Text>
            </View>
          ))}
        </Card>

        <Card style={styles.detailCard}>
          <Text style={styles.sectionTitle}>Features</Text>
          {selectedProduct.features.map((feature, index) => (
            <View key={index} style={styles.featureRow}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </Card>

        <Card style={styles.detailCard}>
          <Text style={styles.sectionTitle}>Sustainability</Text>
          <View style={styles.sustainabilityRow}>
            <Text style={styles.sustainabilityLabel}>Sustainability Score</Text>
            <Text style={styles.sustainabilityScore}>
              {selectedProduct.sustainabilityScore}/10
            </Text>
          </View>
          <Text style={styles.carbonFootprint}>
            Carbon Footprint: {selectedProduct.carbonFootprint}
          </Text>
          <Text style={styles.countryOrigin}>
            Made in {selectedProduct.countryOfOrigin}
          </Text>
        </Card>

        {recommendations && (
          <Card style={styles.detailCard}>
            <Text style={styles.sectionTitle}>AI Recommendation</Text>
            <View style={styles.recommendationHeader}>
              <Ionicons
                name={recommendations.recommendation === 'buy' ? 'thumbs-up' : 'thumbs-down'}
                size={24}
                color={recommendations.recommendation === 'buy' ? Colors.success : Colors.warning}
              />
              <Text style={[
                styles.recommendationText,
                { color: recommendations.recommendation === 'buy' ? Colors.success : Colors.warning }
              ]}>
                {recommendations.recommendation.toUpperCase()}
              </Text>
              <Text style={styles.confidenceText}>
                {Math.round(recommendations.confidence * 100)}% confidence
              </Text>
            </View>
            {recommendations.reasons.map((reason: string, index: number) => (
              <Text key={index} style={styles.reasonText}>• {reason}</Text>
            ))}
          </Card>
        )}
      </ScrollView>
    );
  };

  const renderPriceComparison = () => {
    return (
      <ScrollView style={styles.pricesContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Price Comparison</Text>
        {priceComparison.map((price, index) => (
          <TouchableOpacity
            key={index}
            style={styles.priceCard}
            onPress={() => openProductUrl(price.url)}
          >
            <View style={styles.priceHeader}>
              <Text style={styles.retailerName}>{price.retailer}</Text>
              <Text style={styles.priceValue}>
                ${price.price} {price.currency}
              </Text>
            </View>
            <Text style={styles.availabilityText}>{price.availability}</Text>
            <Text style={styles.shippingText}>{price.shipping}</Text>
            <View style={styles.priceRating}>
              <Ionicons name="star" size={14} color={Colors.warning} />
              <Text style={styles.ratingText}>
                {price.rating} ({price.reviewCount} reviews)
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderReviews = () => {
    return (
      <ScrollView style={styles.reviewsContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Customer Reviews</Text>
        {reviews.map((review) => (
          <Card key={review.id} style={styles.reviewCard}>
            <View style={styles.reviewHeader}>
              <Text style={styles.reviewAuthor}>{review.author}</Text>
              <View style={styles.reviewRating}>
                {[...Array(5)].map((_, i) => (
                  <Ionicons
                    key={i}
                    name="star"
                    size={12}
                    color={i < review.rating ? Colors.warning : Colors.border}
                  />
                ))}
              </View>
            </View>
            <Text style={styles.reviewTitle}>{review.title}</Text>
            <Text style={styles.reviewContent}>{review.content}</Text>
            <View style={styles.reviewFooter}>
              <Text style={styles.reviewSource}>{review.source}</Text>
              <Text style={styles.reviewDate}>
                {new Date(review.date).toLocaleDateString()}
              </Text>
              {review.verified && (
                <View style={styles.verifiedBadge}>
                  <Ionicons name="checkmark-circle" size={12} color={Colors.success} />
                  <Text style={styles.verifiedText}>Verified</Text>
                </View>
              )}
            </View>
          </Card>
        ))}
      </ScrollView>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'results':
        return renderSearchResults();
      case 'details':
        return renderProductDetails();
      case 'prices':
        return renderPriceComparison();
      case 'reviews':
        return renderReviews();
      default:
        return renderSearchResults();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Product Research</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Navigation */}
      {selectedProduct && (
        <View style={styles.tabContainer}>
          {[
            { key: 'results', label: 'Results', icon: 'search' },
            { key: 'details', label: 'Details', icon: 'information-circle' },
            { key: 'prices', label: 'Prices', icon: 'pricetag' },
            { key: 'reviews', label: 'Reviews', icon: 'star' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && styles.activeTab,
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <Ionicons
                name={tab.icon as any}
                size={16}
                color={activeTab === tab.key ? Colors.primary : Colors.textSecondary}
              />
              <Text style={[
                styles.tabText,
                activeTab === tab.key && styles.activeTabText,
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Content */}
      <View style={styles.content}>
        {renderTabContent()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  placeholder: {
    width: 32,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.backgroundSecondary,
    paddingHorizontal: 4,
    paddingVertical: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: Colors.background,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  activeTabText: {
    color: Colors.primary,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  resultsContainer: {
    flex: 1,
    padding: 20,
  },
  resultsHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 16,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  productMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  retailerText: {
    fontSize: 12,
    color: Colors.textLight,
  },
  detailsContainer: {
    flex: 1,
    padding: 20,
  },
  productHeader: {
    marginBottom: 20,
  },
  imageGallery: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  galleryImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginRight: 12,
  },
  productTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  productBrand: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 12,
  },
  detailCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  specRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  specKey: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '600',
  },
  specValue: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
  },
  sustainabilityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sustainabilityLabel: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '600',
  },
  sustainabilityScore: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.success,
  },
  carbonFootprint: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  countryOrigin: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendationText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  confidenceText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  reasonText: {
    fontSize: 14,
    color: Colors.text,
    marginBottom: 4,
  },
  pricesContainer: {
    flex: 1,
    padding: 20,
  },
  priceCard: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  priceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  retailerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
  },
  priceValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  availabilityText: {
    fontSize: 14,
    color: Colors.success,
    marginBottom: 4,
  },
  shippingText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  priceRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewsContainer: {
    flex: 1,
    padding: 20,
  },
  reviewCard: {
    marginBottom: 16,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewAuthor: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text,
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  reviewContent: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  reviewFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  reviewSource: {
    fontSize: 12,
    color: Colors.textLight,
  },
  reviewDate: {
    fontSize: 12,
    color: Colors.textLight,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedText: {
    fontSize: 10,
    color: Colors.success,
    marginLeft: 4,
  },
});
