import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    <PERSON>ert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { <PERSON><PERSON>, Card } from '../src/components';
import { Colors } from '../src/constants/Colors';
import { NotificationService } from '../src/services/NotificationService';
import { useAppStore } from '../src/store/useAppStore';

export default function SettingsScreen() {
  const router = useRouter();
  const { appSettings, updateAppSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const handleNotificationToggle = async (key: keyof typeof appSettings.notifications, value: boolean) => {
    if (key === 'enabled' && value) {
      // Request permissions when enabling notifications
      const hasPermission = await NotificationService.requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'Please enable notifications in your device settings to receive reminders.',
          [{ text: 'OK' }]
        );
        return;
      }
    }

    updateAppSettings({
      notifications: {
        ...appSettings.notifications,
        [key]: value,
      },
    });
  };

  const handlePrivacyToggle = (key: keyof typeof appSettings.privacy, value: boolean) => {
    updateAppSettings({
      privacy: {
        ...appSettings.privacy,
        [key]: value,
      },
    });
  };

  const handleDisplayToggle = (key: keyof typeof appSettings.display, value: boolean) => {
    updateAppSettings({
      display: {
        ...appSettings.display,
        [key]: value,
      },
    });
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
    updateAppSettings({ theme });
  };

  const handleUnitsChange = (units: 'metric' | 'imperial') => {
    updateAppSettings({ units });
  };

  const handleLanguageChange = (language: string) => {
    updateAppSettings({ language });
  };

  const exportData = async () => {
    setIsLoading(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      Alert.alert(
        'Export Complete',
        'Your data has been exported successfully. In a real app, this would save to your device or cloud storage.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Export Failed', 'Failed to export data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const clearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your scans, tasks, and settings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete All',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would clear all data
            Alert.alert('Data Cleared', 'All data has been cleared.');
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Notifications Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Enable Notifications</Text>
              <Text style={styles.settingDescription}>Receive reminders and alerts</Text>
            </View>
            <Switch
              value={appSettings.notifications.enabled}
              onValueChange={(value) => handleNotificationToggle('enabled', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.enabled ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Task Reminders</Text>
              <Text style={styles.settingDescription}>Get notified about upcoming tasks</Text>
            </View>
            <Switch
              value={appSettings.notifications.taskReminders}
              onValueChange={(value) => handleNotificationToggle('taskReminders', value)}
              disabled={!appSettings.notifications.enabled}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.taskReminders ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Plant Care Alerts</Text>
              <Text style={styles.settingDescription}>Reminders for watering and care</Text>
            </View>
            <Switch
              value={appSettings.notifications.plantCareAlerts}
              onValueChange={(value) => handleNotificationToggle('plantCareAlerts', value)}
              disabled={!appSettings.notifications.enabled}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.plantCareAlerts ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Achievement Unlocks</Text>
              <Text style={styles.settingDescription}>Celebrate your progress</Text>
            </View>
            <Switch
              value={appSettings.notifications.achievementUnlocks}
              onValueChange={(value) => handleNotificationToggle('achievementUnlocks', value)}
              disabled={!appSettings.notifications.enabled}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.achievementUnlocks ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Weekly Summaries</Text>
              <Text style={styles.settingDescription}>Weekly activity reports</Text>
            </View>
            <Switch
              value={appSettings.notifications.weeklySummaries}
              onValueChange={(value) => handleNotificationToggle('weeklySummaries', value)}
              disabled={!appSettings.notifications.enabled}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.weeklySummaries ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Quiet Hours</Text>
              <Text style={styles.settingDescription}>
                {appSettings.notifications.quietHoursEnabled 
                  ? `${appSettings.notifications.quietHoursStart} - ${appSettings.notifications.quietHoursEnd}`
                  : 'Disabled'
                }
              </Text>
            </View>
            <Switch
              value={appSettings.notifications.quietHoursEnabled}
              onValueChange={(value) => handleNotificationToggle('quietHoursEnabled', value)}
              disabled={!appSettings.notifications.enabled}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.notifications.quietHoursEnabled ? Colors.primary : Colors.textLight}
            />
          </View>
        </Card>

        {/* Display Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Display</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Theme</Text>
              <Text style={styles.settingDescription}>Choose your preferred theme</Text>
            </View>
          </View>
          
          <View style={styles.themeOptions}>
            {(['light', 'dark', 'auto'] as const).map((theme) => (
              <TouchableOpacity
                key={theme}
                style={[
                  styles.themeOption,
                  appSettings.theme === theme && styles.themeOptionActive,
                ]}
                onPress={() => handleThemeChange(theme)}
              >
                <Text style={[
                  styles.themeOptionText,
                  appSettings.theme === theme && styles.themeOptionTextActive,
                ]}>
                  {theme.charAt(0).toUpperCase() + theme.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>High Contrast</Text>
              <Text style={styles.settingDescription}>Improve text readability</Text>
            </View>
            <Switch
              value={appSettings.display.highContrast}
              onValueChange={(value) => handleDisplayToggle('highContrast', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.display.highContrast ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Reduce Motion</Text>
              <Text style={styles.settingDescription}>Minimize animations</Text>
            </View>
            <Switch
              value={appSettings.display.reduceMotion}
              onValueChange={(value) => handleDisplayToggle('reduceMotion', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.display.reduceMotion ? Colors.primary : Colors.textLight}
            />
          </View>
        </Card>

        {/* Privacy Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Data</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Location Tracking</Text>
              <Text style={styles.settingDescription}>Save location with scans</Text>
            </View>
            <Switch
              value={appSettings.privacy.locationTracking}
              onValueChange={(value) => handlePrivacyToggle('locationTracking', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.privacy.locationTracking ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Analytics</Text>
              <Text style={styles.settingDescription}>Help improve the app</Text>
            </View>
            <Switch
              value={appSettings.privacy.analytics}
              onValueChange={(value) => handlePrivacyToggle('analytics', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={appSettings.privacy.analytics ? Colors.primary : Colors.textLight}
            />
          </View>
        </Card>

        {/* Integrations */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Integrations</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/integrations')}
          >
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Third-Party Apps & Voice Assistant</Text>
              <Text style={styles.settingDescription}>Connect with Google Fit, Siri, productivity apps</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        </Card>

        {/* Widget Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Widget</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/widget-config')}
          >
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Configure Home Screen Widget</Text>
              <Text style={styles.settingDescription}>Customize your widget appearance and content</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        </Card>

        {/* Data Management Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>

          <Button
            title="Export Data"
            onPress={exportData}
            loading={isLoading}
            variant="outline"
            style={styles.actionButton}
          />

          <Button
            title="Clear All Data"
            onPress={clearAllData}
            variant="outline"
            style={[styles.actionButton, { borderColor: Colors.error }]}
            textStyle={{ color: Colors.error }}
          />
        </Card>

        {/* App Info Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Build</Text>
            <Text style={styles.infoValue}>2024.01.01</Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  themeOptions: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  themeOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  themeOptionActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '20',
  },
  themeOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  themeOptionTextActive: {
    color: Colors.primary,
  },
  actionButton: {
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 16,
    color: Colors.text,
  },
  infoValue: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
});
