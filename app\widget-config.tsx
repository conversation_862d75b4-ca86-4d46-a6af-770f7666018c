import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../src/constants/Colors';
import { Card, Button, Input } from '../src/components';
import { WidgetService } from '../src/services/WidgetService';
import { WidgetConfig, WidgetData } from '../src/types';

export default function WidgetConfigScreen() {
  const router = useRouter();
  const [config, setConfig] = useState<WidgetConfig | null>(null);
  const [previewData, setPreviewData] = useState<WidgetData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const [widgetConfig, preview] = await Promise.all([
        WidgetService.getWidgetConfig(),
        Promise.resolve(WidgetService.generatePreviewData()),
      ]);
      
      setConfig(widgetConfig);
      setPreviewData(preview);
    } catch (error) {
      console.error('Error loading widget config:', error);
      Alert.alert('Error', 'Failed to load widget configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfig = async (updates: Partial<WidgetConfig>) => {
    if (!config) return;

    const newConfig = { ...config, ...updates };
    setConfig(newConfig);

    try {
      await WidgetService.updateWidgetConfig(updates);
    } catch (error) {
      console.error('Error updating widget config:', error);
      Alert.alert('Error', 'Failed to update widget configuration');
    }
  };

  const handleToggle = (key: keyof WidgetConfig, value: boolean) => {
    updateConfig({ [key]: value });
  };

  const handleNumberChange = (key: keyof WidgetConfig, value: number) => {
    updateConfig({ [key]: value });
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
    updateConfig({ theme });
  };

  const renderWidgetPreview = (size: 'small' | 'medium' | 'large') => {
    if (!previewData || !config) return null;

    const formattedData = WidgetService.formatForWidgetSize(previewData, size);
    
    return (
      <View style={[styles.widgetPreview, styles[`${size}Preview`]]}>
        <Text style={styles.previewTitle}>BioScan</Text>
        
        {formattedData.stats && (
          <View style={styles.previewStats}>
            <View style={styles.previewStat}>
              <Text style={styles.previewStatValue}>{formattedData.stats.totalScans}</Text>
              <Text style={styles.previewStatLabel}>Scans</Text>
            </View>
            <View style={styles.previewStat}>
              <Text style={styles.previewStatValue}>{formattedData.stats.pendingTasks}</Text>
              <Text style={styles.previewStatLabel}>Tasks</Text>
            </View>
          </View>
        )}

        {size !== 'small' && formattedData.recentScans && (
          <View style={styles.previewScans}>
            {formattedData.recentScans.slice(0, size === 'medium' ? 1 : 2).map((scan, index) => (
              <Text key={index} style={styles.previewScanItem} numberOfLines={1}>
                {scan.name}
              </Text>
            ))}
          </View>
        )}

        {formattedData.quickActions && (
          <View style={styles.previewActions}>
            {formattedData.quickActions.map((action, index) => (
              <View key={index} style={styles.previewAction}>
                <Ionicons name={action.icon as any} size={16} color={Colors.primary} />
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  if (isLoading || !config) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loading}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Widget Configuration</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Widget Previews */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Widget Previews</Text>
          
          <View style={styles.previewContainer}>
            <View style={styles.previewRow}>
              <View style={styles.previewWrapper}>
                <Text style={styles.previewLabel}>Small</Text>
                {renderWidgetPreview('small')}
              </View>
              <View style={styles.previewWrapper}>
                <Text style={styles.previewLabel}>Medium</Text>
                {renderWidgetPreview('medium')}
              </View>
            </View>
            <View style={styles.previewRow}>
              <View style={styles.previewWrapper}>
                <Text style={styles.previewLabel}>Large</Text>
                {renderWidgetPreview('large')}
              </View>
            </View>
          </View>
        </Card>

        {/* Content Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Content</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Show Statistics</Text>
              <Text style={styles.settingDescription}>Display scan and task counts</Text>
            </View>
            <Switch
              value={config.showStats}
              onValueChange={(value) => handleToggle('showStats', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={config.showStats ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Show Recent Scans</Text>
              <Text style={styles.settingDescription}>Display latest scanned items</Text>
            </View>
            <Switch
              value={config.showRecentScans}
              onValueChange={(value) => handleToggle('showRecentScans', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={config.showRecentScans ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Show Upcoming Tasks</Text>
              <Text style={styles.settingDescription}>Display pending tasks</Text>
            </View>
            <Switch
              value={config.showUpcomingTasks}
              onValueChange={(value) => handleToggle('showUpcomingTasks', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={config.showUpcomingTasks ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Show Achievements</Text>
              <Text style={styles.settingDescription}>Display unlocked achievements</Text>
            </View>
            <Switch
              value={config.showAchievements}
              onValueChange={(value) => handleToggle('showAchievements', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={config.showAchievements ? Colors.primary : Colors.textLight}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Show Quick Actions</Text>
              <Text style={styles.settingDescription}>Display action buttons</Text>
            </View>
            <Switch
              value={config.showQuickActions}
              onValueChange={(value) => handleToggle('showQuickActions', value)}
              trackColor={{ false: Colors.border, true: Colors.primary + '50' }}
              thumbColor={config.showQuickActions ? Colors.primary : Colors.textLight}
            />
          </View>
        </Card>

        {/* Appearance Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Theme</Text>
              <Text style={styles.settingDescription}>Choose widget theme</Text>
            </View>
          </View>
          
          <View style={styles.themeOptions}>
            {(['light', 'dark', 'auto'] as const).map((theme) => (
              <TouchableOpacity
                key={theme}
                style={[
                  styles.themeOption,
                  config.theme === theme && styles.themeOptionActive,
                ]}
                onPress={() => handleThemeChange(theme)}
              >
                <Text style={[
                  styles.themeOptionText,
                  config.theme === theme && styles.themeOptionTextActive,
                ]}>
                  {theme.charAt(0).toUpperCase() + theme.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Instructions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>How to Add Widget</Text>
          
          <View style={styles.instructions}>
            <Text style={styles.instructionTitle}>iOS:</Text>
            <Text style={styles.instructionText}>
              1. Long press on your home screen{'\n'}
              2. Tap the "+" button{'\n'}
              3. Search for "BioScan"{'\n'}
              4. Select widget size and tap "Add Widget"
            </Text>
            
            <Text style={styles.instructionTitle}>Android:</Text>
            <Text style={styles.instructionText}>
              1. Long press on your home screen{'\n'}
              2. Tap "Widgets"{'\n'}
              3. Find "BioScan" in the list{'\n'}
              4. Drag the widget to your home screen
            </Text>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  previewContainer: {
    alignItems: 'center',
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  previewWrapper: {
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  widgetPreview: {
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  smallPreview: {
    width: 80,
    height: 80,
  },
  mediumPreview: {
    width: 120,
    height: 80,
  },
  largePreview: {
    width: 200,
    height: 120,
  },
  previewTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  previewStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  previewStat: {
    alignItems: 'center',
  },
  previewStatValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  previewStatLabel: {
    fontSize: 8,
    color: Colors.textSecondary,
  },
  previewScans: {
    marginBottom: 4,
  },
  previewScanItem: {
    fontSize: 8,
    color: Colors.text,
  },
  previewActions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  previewAction: {
    marginHorizontal: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  themeOptions: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  themeOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  themeOptionActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '20',
  },
  themeOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  themeOptionTextActive: {
    color: Colors.primary,
  },
  instructions: {
    marginTop: 8,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 12,
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
});
