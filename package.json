{"name": "apex", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx expo start", "reset-project": "node ./scripts/reset-project.js", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "lint": "npx expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.50.4", "expo": "^53.0.0", "expo-av": "^15.1.7", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.5", "expo-calendar": "^14.1.4", "expo-camera": "^16.1.10", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.7", "expo-location": "^18.1.6", "expo-modules-core": "^2.4.2", "expo-notifications": "^0.31.4", "expo-router": "~5.1.3", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "firebase": "^11.9.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-async-storage": "^0.0.1", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "turso": "^0.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "expo": {"newArchEnabled": true, "doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-redux"]}}}}