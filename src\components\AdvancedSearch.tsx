import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { Colors } from '../constants/Colors';
import { SavedSearchFilter, SearchFilters } from '../types';
import { Button, Card, Input } from './';

interface AdvancedSearchProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: SearchFilters) => void;
  onSaveFilter: (filter: SavedSearchFilter) => void;
  savedFilters: SavedSearchFilter[];
  currentFilters: SearchFilters;
}

export const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  visible,
  onClose,
  onApplyFilters,
  onSaveFilter,
  savedFilters,
  currentFilters,
}) => {
  const [filters, setFilters] = useState<SearchFilters>(currentFilters);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [filterName, setFilterName] = useState('');

  const categories = ['food', 'plants', 'animals', 'rocks', 'coins'];

  const resetFilters = () => {
    setFilters({});
  };

  const applyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  const saveCurrentFilter = () => {
    if (!filterName.trim()) return;
    
    const savedFilter: SavedSearchFilter = {
      id: Date.now().toString(),
      name: filterName.trim(),
      filters,
      createdAt: new Date(),
    };
    
    onSaveFilter(savedFilter);
    setFilterName('');
    setShowSaveDialog(false);
  };

  const loadSavedFilter = (savedFilter: SavedSearchFilter) => {
    setFilters(savedFilter.filters);
  };

  const toggleCategory = (category: string) => {
    const currentCategories = filters.category || [];
    const updatedCategories = currentCategories.includes(category)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category];
    
    setFilters({
      ...filters,
      category: updatedCategories.length > 0 ? updatedCategories : undefined,
    });
  };

  const updateConfidenceRange = (min: number, max: number) => {
    setFilters({
      ...filters,
      confidence: { min: min / 100, max: max / 100 },
    });
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Advanced Search</Text>
          <TouchableOpacity onPress={resetFilters}>
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Saved Filters */}
          {savedFilters.length > 0 && (
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Saved Filters</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.savedFiltersContainer}>
                  {savedFilters.map((savedFilter) => (
                    <TouchableOpacity
                      key={savedFilter.id}
                      style={styles.savedFilterChip}
                      onPress={() => loadSavedFilter(savedFilter)}
                    >
                      <Text style={styles.savedFilterText}>{savedFilter.name}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </Card>
          )}

          {/* Categories */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Categories</Text>
            <View style={styles.categoriesGrid}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryChip,
                    filters.category?.includes(category) && styles.categoryChipActive,
                  ]}
                  onPress={() => toggleCategory(category)}
                >
                  <Text
                    style={[
                      styles.categoryChipText,
                      filters.category?.includes(category) && styles.categoryChipTextActive,
                    ]}
                  >
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          {/* Date Range */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Date Range</Text>
            <View style={styles.dateRangeContainer}>
              <TouchableOpacity style={styles.dateButton}>
                <Text style={styles.dateButtonText}>
                  {filters.dateRange?.start 
                    ? filters.dateRange.start.toLocaleDateString()
                    : 'Start Date'
                  }
                </Text>
                <Ionicons name={"calendar-outline" as any} size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
              
              <Text style={styles.dateRangeSeparator}>to</Text>
              
              <TouchableOpacity style={styles.dateButton}>
                <Text style={styles.dateButtonText}>
                  {filters.dateRange?.end 
                    ? filters.dateRange.end.toLocaleDateString()
                    : 'End Date'
                  }
                </Text>
                <Ionicons name={"calendar-outline" as any} size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
            </View>
          </Card>

          {/* Confidence Level */}
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Confidence Level</Text>
            <View style={styles.confidenceContainer}>
              <Text style={styles.confidenceLabel}>
                {Math.round((filters.confidence?.min || 0) * 100)}% - {Math.round((filters.confidence?.max || 1) * 100)}%
              </Text>
              <View style={styles.confidenceSliders}>
                <Text style={styles.sliderLabel}>Min: {Math.round((filters.confidence?.min || 0) * 100)}%</Text>
                <Text style={styles.sliderLabel}>Max: {Math.round((filters.confidence?.max || 1) * 100)}%</Text>
              </View>
            </View>
          </Card>

          {/* Location */}
          <Card style={styles.section}>
            <Input
              label="Location"
              placeholder="Enter location..."
              value={filters.location || ''}
              onChangeText={(text) => setFilters({ ...filters, location: text || undefined })}
              leftIcon={"location-outline" as any}
            />
          </Card>

          {/* Tags */}
          <Card style={styles.section}>
            <Input
              label="Tags"
              placeholder="Enter tags (comma separated)..."
              value={filters.tags?.join(', ') || ''}
              onChangeText={(text) => {
                const tags = text.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
                setFilters({ ...filters, tags: tags.length > 0 ? tags : undefined });
              }}
              leftIcon={"pricetag-outline" as any}
            />
          </Card>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Button
            title="Save Filter"
            variant="outline"
            onPress={() => setShowSaveDialog(true)}
            style={styles.saveButton}
          />
          <Button
            title="Apply Filters"
            onPress={applyFilters}
            style={styles.applyButton}
          />
        </View>

        {/* Save Filter Dialog */}
        <Modal visible={showSaveDialog} transparent animationType="fade">
          <View style={styles.saveDialogOverlay}>
            <View style={styles.saveDialog}>
              <Text style={styles.saveDialogTitle}>Save Filter</Text>
              <Input
                placeholder="Enter filter name..."
                value={filterName}
                onChangeText={setFilterName}
              />
              <View style={styles.saveDialogButtons}>
                <Button
                  title="Cancel"
                  variant="outline"
                  onPress={() => setShowSaveDialog(false)}
                  style={styles.saveDialogButton}
                />
                <Button
                  title="Save"
                  onPress={saveCurrentFilter}
                  style={styles.saveDialogButton}
                />
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  cancelText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  resetText: {
    fontSize: 16,
    color: Colors.primary,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  savedFiltersContainer: {
    flexDirection: 'row',
  },
  savedFilterChip: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  savedFilterText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '600',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    backgroundColor: Colors.backgroundSecondary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryChipActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryChipText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '600',
  },
  categoryChipTextActive: {
    color: Colors.textInverse,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: Colors.text,
  },
  dateRangeSeparator: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginHorizontal: 12,
  },
  confidenceContainer: {
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  confidenceSliders: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  sliderLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  saveButton: {
    flex: 1,
    marginRight: 10,
  },
  applyButton: {
    flex: 1,
    marginLeft: 10,
  },
  saveDialogOverlay: {
    flex: 1,
    backgroundColor: Colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveDialog: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 20,
    width: '80%',
    maxWidth: 300,
  },
  saveDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  saveDialogButtons: {
    flexDirection: 'row',
    marginTop: 16,
  },
  saveDialogButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});
