import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  StyleProp, // Import StyleProp
} from 'react-native';
import { Colors } from '../constants/Colors';

interface CardProps {
  children: React.ReactNode;
  // FIX: Change 'ViewStyle' to 'StyleProp<ViewStyle>' to allow single style, array of styles, or undefined.
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  onPress,
  variant = 'default',
  padding = 'medium',
}) => {
  // Ensure that all individual styles are correctly applied.
  // The 'style' prop from outside will be merged last.
  const cardStyle = [
    styles.base,
    styles[variant],
    styles[padding],
    style, // 'style' can now be an array or single object
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle} // This will now correctly accept the array of styles
        onPress={onPress}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyle}> {/* This will now correctly accept the array of styles */}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 16,
    backgroundColor: Colors.background,
  },

  // Variants
  default: {
    backgroundColor: Colors.backgroundSecondary,
  },
  elevated: {
    backgroundColor: Colors.background,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  outlined: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Padding
  none: {
    padding: 0,
  },
  small: {
    padding: 12,
  },
  medium: {
    padding: 16,
  },
  large: {
    padding: 20,
  },
});