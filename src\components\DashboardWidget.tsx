import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { Card } from './Card';
import { DashboardWidgetConfig, ScanHistory, TodoItem } from '../types';

const { width } = Dimensions.get('window');

interface DashboardWidgetProps {
  config: DashboardWidgetConfig;
  data?: any;
  onPress?: () => void;
  onEdit?: () => void;
  isEditing?: boolean;
}

export const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  config,
  data,
  onPress,
  onEdit,
  isEditing = false,
}) => {
  const getWidgetSize = () => {
    const padding = 40; // Total horizontal padding
    const gap = 16; // Gap between widgets
    
    switch (config.size) {
      case 'small':
        return { width: (width - padding - gap) / 2, height: 120 };
      case 'large':
        return { width: width - padding, height: 200 };
      default: // medium
        return { width: width - padding, height: 150 };
    }
  };

  const renderWidgetContent = () => {
    switch (config.type) {
      case 'recent_scans':
        return renderRecentScans();
      case 'upcoming_tasks':
        return renderUpcomingTasks();
      case 'achievements':
        return renderAchievements();
      case 'stats':
        return renderStats();
      case 'weather':
        return renderWeather();
      case 'categories':
        return renderCategories();
      default:
        return renderDefault();
    }
  };

  const renderRecentScans = () => {
    const recentScans = data?.recentScans || [];
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"camera" as any} size={20} color={Colors.primary} />
          <Text style={styles.widgetTitle}>Recent Scans</Text>
        </View>
        
        {recentScans.length > 0 ? (
          <View style={styles.scansList}>
            {recentScans.slice(0, 3).map((scan: ScanHistory, index: number) => (
              <View key={scan.id} style={styles.scanItem}>
                <Text style={styles.scanName} numberOfLines={1}>
                  {scan.result.name}
                </Text>
                <Text style={styles.scanCategory}>
                  {scan.result.category}
                </Text>
              </View>
            ))}
          </View>
        ) : (
          <Text style={styles.emptyText}>No recent scans</Text>
        )}
      </View>
    );
  };

  const renderUpcomingTasks = () => {
    const upcomingTasks = data?.upcomingTasks || [];
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"checkmark-circle" as any} size={20} color={Colors.accent} />
          <Text style={styles.widgetTitle}>Upcoming Tasks</Text>
        </View>
        
        {upcomingTasks.length > 0 ? (
          <View style={styles.tasksList}>
            {upcomingTasks.slice(0, 3).map((task: TodoItem, index: number) => (
              <View key={task.id} style={styles.taskItem}>
                <Text style={styles.taskTitle} numberOfLines={1}>
                  {task.title}
                </Text>
                <Text style={styles.taskDue}>
                  {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No date'}
                </Text>
              </View>
            ))}
          </View>
        ) : (
          <Text style={styles.emptyText}>No upcoming tasks</Text>
        )}
      </View>
    );
  };

  const renderAchievements = () => {
    const achievements = data?.achievements || [];
    const unlockedCount = achievements.filter((a: any) => a.unlocked).length;
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"trophy" as any} size={20} color={Colors.warning} />
          <Text style={styles.widgetTitle}>Achievements</Text>
        </View>
        
        <View style={styles.achievementStats}>
          <Text style={styles.achievementCount}>{unlockedCount}</Text>
          <Text style={styles.achievementLabel}>Unlocked</Text>
        </View>
        
        {achievements.length > 0 && (
          <Text style={styles.latestAchievement} numberOfLines={2}>
            Latest: {achievements[achievements.length - 1]?.title || 'None'}
          </Text>
        )}
      </View>
    );
  };

  const renderStats = () => {
    const stats = data?.stats || {};
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"bar-chart" as any} size={20} color={Colors.info} />
          <Text style={styles.widgetTitle}>Statistics</Text>
        </View>
        
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.totalScans || 0}</Text>
            <Text style={styles.statLabel}>Scans</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.completedTasks || 0}</Text>
            <Text style={styles.statLabel}>Tasks</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderWeather = () => {
    const weather = data?.weather || {};
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"partly-sunny" as any} size={20} color={Colors.warning} />
          <Text style={styles.widgetTitle}>Weather</Text>
        </View>
        
        <View style={styles.weatherInfo}>
          <Text style={styles.temperature}>
            {weather.temperature || '--'}°
          </Text>
          <Text style={styles.weatherCondition}>
            {weather.condition || 'Loading...'}
          </Text>
          <Text style={styles.plantCareAdvice}>
            {weather.plantCareAdvice || 'Good day for plant care!'}
          </Text>
        </View>
      </View>
    );
  };

  const renderCategories = () => {
    const categories = data?.categories || [];
    
    return (
      <View style={styles.widgetContent}>
        <View style={styles.widgetHeader}>
          <Ionicons name={"grid" as any} size={20} color={Colors.accent} />
          <Text style={styles.widgetTitle}>Categories</Text>
        </View>
        
        <View style={styles.categoriesGrid}>
          {categories.slice(0, 4).map((category: any, index: number) => (
            <View key={index} style={styles.categoryItem}>
              <Text style={styles.categoryCount}>{category.count}</Text>
              <Text style={styles.categoryName}>{category.name}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderDefault = () => (
    <View style={styles.widgetContent}>
      <View style={styles.widgetHeader}>
        <Ionicons name="help-circle" size={20} color={Colors.textSecondary} />
        <Text style={styles.widgetTitle}>{config.title}</Text>
      </View>
      <Text style={styles.emptyText}>Widget content not available</Text>
    </View>
  );

  const widgetSize = getWidgetSize();

  return (
    <TouchableOpacity
      style={[styles.widget, { width: widgetSize.width, height: widgetSize.height }]}
      onPress={onPress}
      disabled={isEditing}
    >
      <Card style={styles.widgetCard}>
        {isEditing && (
          <TouchableOpacity style={styles.editButton} onPress={onEdit}>
            <Ionicons name="settings" size={16} color={Colors.textSecondary} />
          </TouchableOpacity>
        )}
        {renderWidgetContent()}
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  widget: {
    marginBottom: 16,
  },
  widgetCard: {
    flex: 1,
    padding: 16,
  },
  editButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 4,
  },
  widgetContent: {
    flex: 1,
  },
  widgetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  widgetTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 8,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 20,
  },
  scansList: {
    flex: 1,
  },
  scanItem: {
    marginBottom: 8,
  },
  scanName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  scanCategory: {
    fontSize: 12,
    color: Colors.textSecondary,
    textTransform: 'capitalize',
  },
  tasksList: {
    flex: 1,
  },
  taskItem: {
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  taskDue: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  achievementStats: {
    alignItems: 'center',
    marginVertical: 12,
  },
  achievementCount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.warning,
  },
  achievementLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  latestAchievement: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.info,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  weatherInfo: {
    alignItems: 'center',
    marginTop: 8,
  },
  temperature: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.warning,
  },
  weatherCondition: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  plantCareAdvice: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  categoryItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryCount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.accent,
  },
  categoryName: {
    fontSize: 12,
    color: Colors.textSecondary,
    textTransform: 'capitalize',
  },
});
