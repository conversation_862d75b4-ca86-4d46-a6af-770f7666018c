import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { Card, Button, Input } from './';
import { useAppStore } from '../store/useAppStore';
import { NutritionEntry } from '../types';

interface NutritionTrackerProps {
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export const NutritionTracker: React.FC<NutritionTrackerProps> = ({
  dateRange = {
    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    end: new Date(),
  },
}) => {
  const { nutritionEntries, addNutritionEntry, getNutritionEntries } = useAppStore();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newEntry, setNewEntry] = useState({
    foodItem: '',
    quantity: '1',
    unit: 'serving',
    mealType: 'snack' as 'breakfast' | 'lunch' | 'dinner' | 'snack',
  });

  const [filteredEntries, setFilteredEntries] = useState<NutritionEntry[]>([]);

  useEffect(() => {
    const entries = getNutritionEntries(dateRange.start, dateRange.end);
    setFilteredEntries(entries);
  }, [nutritionEntries, dateRange]);

  const handleAddEntry = async () => {
    if (!newEntry.foodItem.trim()) {
      Alert.alert('Error', 'Please enter a food item');
      return;
    }

    const entry: NutritionEntry = {
      id: `manual_${Date.now()}`,
      foodItem: newEntry.foodItem.trim(),
      quantity: parseFloat(newEntry.quantity) || 1,
      unit: newEntry.unit,
      timestamp: new Date().toISOString(),
      mealType: newEntry.mealType,
      nutrients: await estimateNutrients(newEntry.foodItem),
      source: 'manual_entry',
    };

    addNutritionEntry(entry);
    setNewEntry({
      foodItem: '',
      quantity: '1',
      unit: 'serving',
      mealType: 'snack',
    });
    setShowAddForm(false);
  };

  const estimateNutrients = async (foodItem: string): Promise<Record<string, number>> => {
    // Basic nutrition estimation
    return {
      calories: 100,
      protein: 5,
      carbs: 20,
      fat: 3,
      fiber: 2,
      sugar: 10,
    };
  };

  const getTotalNutrients = () => {
    const totals = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      fiber: 0,
    };

    filteredEntries.forEach(entry => {
      if (entry.nutrients) {
        totals.calories += entry.nutrients.calories || 0;
        totals.protein += entry.nutrients.protein || 0;
        totals.carbs += entry.nutrients.carbs || 0;
        totals.fat += entry.nutrients.fat || 0;
        totals.fiber += entry.nutrients.fiber || 0;
      }
    });

    return totals;
  };

  const groupEntriesByMeal = () => {
    const grouped: Record<string, NutritionEntry[]> = {
      breakfast: [],
      lunch: [],
      dinner: [],
      snack: [],
    };

    filteredEntries.forEach(entry => {
      grouped[entry.mealType].push(entry);
    });

    return grouped;
  };

  const renderNutritionSummary = () => {
    const totals = getTotalNutrients();

    return (
      <Card style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Daily Nutrition Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{Math.round(totals.calories)}</Text>
            <Text style={styles.summaryLabel}>Calories</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{Math.round(totals.protein)}g</Text>
            <Text style={styles.summaryLabel}>Protein</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{Math.round(totals.carbs)}g</Text>
            <Text style={styles.summaryLabel}>Carbs</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{Math.round(totals.fat)}g</Text>
            <Text style={styles.summaryLabel}>Fat</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderMealSection = (mealType: string, entries: NutritionEntry[]) => {
    const mealIcons = {
      breakfast: 'sunny',
      lunch: 'partly-sunny',
      dinner: 'moon',
      snack: 'cafe',
    };

    return (
      <Card key={mealType} style={styles.mealCard}>
        <View style={styles.mealHeader}>
          <Ionicons 
            name={mealIcons[mealType as keyof typeof mealIcons] as any} 
            size={20} 
            color={Colors.primary} 
          />
          <Text style={styles.mealTitle}>
            {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
          </Text>
          <Text style={styles.mealCount}>({entries.length})</Text>
        </View>

        {entries.length > 0 ? (
          entries.map((entry) => (
            <View key={entry.id} style={styles.entryItem}>
              <View style={styles.entryInfo}>
                <Text style={styles.entryFood}>{entry.foodItem}</Text>
                <Text style={styles.entryDetails}>
                  {entry.quantity} {entry.unit}
                  {entry.nutrients?.calories && ` • ${Math.round(entry.nutrients.calories)} cal`}
                </Text>
              </View>
              <View style={styles.entrySource}>
                <Ionicons 
                  name={entry.source === 'bioscan_app' ? 'camera' : 'create'} 
                  size={14} 
                  color={Colors.textSecondary} 
                />
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.emptyMealText}>No items logged</Text>
        )}
      </Card>
    );
  };

  const renderAddForm = () => {
    if (!showAddForm) return null;

    return (
      <Card style={styles.addFormCard}>
        <Text style={styles.addFormTitle}>Add Food Item</Text>
        
        <Input
          label="Food Item"
          placeholder="Enter food name..."
          value={newEntry.foodItem}
          onChangeText={(text) => setNewEntry({ ...newEntry, foodItem: text })}
        />

        <View style={styles.formRow}>
          <View style={styles.formColumn}>
            <Input
              label="Quantity"
              placeholder="1"
              value={newEntry.quantity}
              onChangeText={(text) => setNewEntry({ ...newEntry, quantity: text })}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.formColumn}>
            <Text style={styles.inputLabel}>Unit</Text>
            <View style={styles.unitSelector}>
              {['serving', 'cup', 'piece', 'gram'].map((unit) => (
                <TouchableOpacity
                  key={unit}
                  style={[
                    styles.unitOption,
                    newEntry.unit === unit && styles.unitOptionActive,
                  ]}
                  onPress={() => setNewEntry({ ...newEntry, unit })}
                >
                  <Text style={[
                    styles.unitOptionText,
                    newEntry.unit === unit && styles.unitOptionTextActive,
                  ]}>
                    {unit}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <Text style={styles.inputLabel}>Meal Type</Text>
        <View style={styles.mealTypeSelector}>
          {(['breakfast', 'lunch', 'dinner', 'snack'] as const).map((meal) => (
            <TouchableOpacity
              key={meal}
              style={[
                styles.mealTypeOption,
                newEntry.mealType === meal && styles.mealTypeOptionActive,
              ]}
              onPress={() => setNewEntry({ ...newEntry, mealType: meal })}
            >
              <Text style={[
                styles.mealTypeOptionText,
                newEntry.mealType === meal && styles.mealTypeOptionTextActive,
              ]}>
                {meal.charAt(0).toUpperCase() + meal.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.formButtons}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => setShowAddForm(false)}
            style={styles.formButton}
          />
          <Button
            title="Add Item"
            onPress={handleAddEntry}
            style={styles.formButton}
          />
        </View>
      </Card>
    );
  };

  const groupedEntries = groupEntriesByMeal();

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderNutritionSummary()}
      
      <View style={styles.addButtonContainer}>
        <Button
          title="Add Food Item"
          onPress={() => setShowAddForm(true)}
          leftIcon="add"
          style={styles.addButton}
        />
      </View>

      {renderAddForm()}

      <View style={styles.mealsContainer}>
        {Object.entries(groupedEntries).map(([mealType, entries]) =>
          renderMealSection(mealType, entries)
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  summaryLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  addButtonContainer: {
    marginBottom: 16,
  },
  addButton: {
    marginHorizontal: 0,
  },
  addFormCard: {
    marginBottom: 16,
  },
  addFormTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formColumn: {
    flex: 1,
    marginHorizontal: 4,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  unitSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  unitOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    marginRight: 8,
    marginBottom: 8,
  },
  unitOptionActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '20',
  },
  unitOptionText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  unitOptionTextActive: {
    color: Colors.primary,
    fontWeight: '600',
  },
  mealTypeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  mealTypeOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    marginHorizontal: 2,
    alignItems: 'center',
  },
  mealTypeOptionActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '20',
  },
  mealTypeOptionText: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '600',
  },
  mealTypeOptionTextActive: {
    color: Colors.primary,
  },
  formButtons: {
    flexDirection: 'row',
    marginTop: 16,
  },
  formButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  mealsContainer: {
    marginBottom: 20,
  },
  mealCard: {
    marginBottom: 16,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  mealTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 8,
  },
  mealCount: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  entryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  entryInfo: {
    flex: 1,
  },
  entryFood: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  entryDetails: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  entrySource: {
    marginLeft: 8,
  },
  emptyMealText: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 16,
  },
});
