import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { VoiceSearchBar } from './VoiceSearchBar';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ProductResearchFABProps {
  style?: any;
}

export const ProductResearchFAB: React.FC<ProductResearchFABProps> = ({ style }) => {
  const router = useRouter();
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;

  const showSearchBar = () => {
    setIsSearchVisible(true);
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideSearchBar = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsSearchVisible(false);
    });
  };

  const handleSearch = (query: string) => {
    hideSearchBar();
    router.push(`/product-search?query=${encodeURIComponent(query)}`);
  };

  const handleVoiceStart = () => {
    console.log('Voice search started');
  };

  const handleVoiceEnd = () => {
    console.log('Voice search ended');
  };

  return (
    <>
      {/* Search Bar Overlay */}
      {isSearchVisible && (
        <Animated.View
          style={[
            styles.searchOverlay,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <VoiceSearchBar
            onSearch={handleSearch}
            onVoiceStart={handleVoiceStart}
            onVoiceEnd={handleVoiceEnd}
            placeholder="Search products or speak..."
          />
          
          {/* Close overlay when tapping outside */}
          <TouchableOpacity
            style={styles.overlayBackground}
            onPress={hideSearchBar}
            activeOpacity={1}
          />
        </Animated.View>
      )}

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, style]}
        onPress={showSearchBar}
        activeOpacity={0.8}
      >
        <Ionicons name="search" size={24} color={Colors.textInverse} />
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom: 100, // Above tab bar
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.accent,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1000,
  },
  searchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1001,
  },
  overlayBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
});
