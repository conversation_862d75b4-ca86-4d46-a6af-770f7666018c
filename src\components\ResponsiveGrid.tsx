import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  Dimensions,
} from 'react-native';
import { getLayoutConfig, getGridColumns } from '../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');

interface ResponsiveGridProps {
  children: React.ReactNode[];
  spacing?: number;
  minItemWidth?: number;
  maxColumns?: number;
  style?: ViewStyle;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing = 16,
  minItemWidth = 150,
  maxColumns = 4,
  style,
}) => {
  const layoutConfig = getLayoutConfig();
  
  // Calculate optimal number of columns
  const calculateColumns = () => {
    const availableWidth = screenWidth - (layoutConfig.padding * 2);
    const columnsFromWidth = Math.floor(availableWidth / (minItemWidth + spacing));
    const columnsFromDevice = getGridColumns(2);
    
    return Math.min(Math.max(columnsFromWidth, 1), maxColumns, columnsFromDevice);
  };

  const columns = calculateColumns();
  const itemWidth = (screenWidth - (layoutConfig.padding * 2) - (spacing * (columns - 1))) / columns;

  // Group children into rows
  const rows: React.ReactNode[][] = [];
  for (let i = 0; i < children.length; i += columns) {
    rows.push(children.slice(i, i + columns));
  }

  return (
    <View style={[styles.container, style]}>
      {rows.map((row, rowIndex) => (
        <View key={rowIndex} style={[styles.row, { marginBottom: spacing }]}>
          {row.map((child, itemIndex) => (
            <View
              key={itemIndex}
              style={[
                styles.item,
                {
                  width: itemWidth,
                  marginRight: itemIndex < row.length - 1 ? spacing : 0,
                },
              ]}
            >
              {child}
            </View>
          ))}
          {/* Fill remaining space if row is not complete */}
          {row.length < columns && (
            <View style={{ flex: 1 }} />
          )}
        </View>
      ))}
    </View>
  );
};

interface ResponsiveMasonryProps {
  children: React.ReactNode[];
  spacing?: number;
  style?: ViewStyle;
}

export const ResponsiveMasonry: React.FC<ResponsiveMasonryProps> = ({
  children,
  spacing = 16,
  style,
}) => {
  const layoutConfig = getLayoutConfig();
  const columns = getGridColumns(2);
  
  // Distribute children across columns
  const columnArrays: React.ReactNode[][] = Array.from({ length: columns }, () => []);
  
  children.forEach((child, index) => {
    const columnIndex = index % columns;
    columnArrays[columnIndex].push(child);
  });

  const columnWidth = (screenWidth - (layoutConfig.padding * 2) - (spacing * (columns - 1))) / columns;

  return (
    <View style={[styles.masonryContainer, style]}>
      {columnArrays.map((columnChildren, columnIndex) => (
        <View
          key={columnIndex}
          style={[
            styles.masonryColumn,
            {
              width: columnWidth,
              marginRight: columnIndex < columnArrays.length - 1 ? spacing : 0,
            },
          ]}
        >
          {columnChildren.map((child, itemIndex) => (
            <View
              key={itemIndex}
              style={[
                styles.masonryItem,
                { marginBottom: itemIndex < columnChildren.length - 1 ? spacing : 0 },
              ]}
            >
              {child}
            </View>
          ))}
        </View>
      ))}
    </View>
  );
};

interface ResponsiveListProps {
  children: React.ReactNode[];
  horizontal?: boolean;
  spacing?: number;
  style?: ViewStyle;
}

export const ResponsiveList: React.FC<ResponsiveListProps> = ({
  children,
  horizontal = false,
  spacing = 12,
  style,
}) => {
  const layoutConfig = getLayoutConfig();

  if (horizontal) {
    return (
      <View style={[styles.horizontalList, style]}>
        {children.map((child, index) => (
          <View
            key={index}
            style={[
              styles.horizontalItem,
              { marginRight: index < children.length - 1 ? spacing : 0 },
            ]}
          >
            {child}
          </View>
        ))}
      </View>
    );
  }

  return (
    <View style={[styles.verticalList, style]}>
      {children.map((child, index) => (
        <View
          key={index}
          style={[
            styles.verticalItem,
            { marginBottom: index < children.length - 1 ? spacing : 0 },
          ]}
        >
          {child}
        </View>
      ))}
    </View>
  );
};

interface ResponsiveCardGridProps {
  data: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  keyExtractor?: (item: any, index: number) => string;
  spacing?: number;
  minItemWidth?: number;
  style?: ViewStyle;
}

export const ResponsiveCardGrid: React.FC<ResponsiveCardGridProps> = ({
  data,
  renderItem,
  keyExtractor = (_, index) => index.toString(),
  spacing = 16,
  minItemWidth = 200,
  style,
}) => {
  const children = data.map((item, index) => (
    <View key={keyExtractor(item, index)}>
      {renderItem(item, index)}
    </View>
  ));

  return (
    <ResponsiveGrid
      spacing={spacing}
      minItemWidth={minItemWidth}
      style={style}
    >
      {children}
    </ResponsiveGrid>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  item: {
    flex: 0,
  },
  masonryContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  masonryColumn: {
    flex: 0,
  },
  masonryItem: {
    width: '100%',
  },
  horizontalList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  horizontalItem: {
    flex: 0,
  },
  verticalList: {
    flexDirection: 'column',
  },
  verticalItem: {
    width: '100%',
  },
});
