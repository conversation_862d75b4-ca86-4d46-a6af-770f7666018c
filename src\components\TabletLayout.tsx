import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { getLayoutConfig, isTablet } from '../utils/responsive';

const { width: screenWidth } = Dimensions.get('window');

interface TabletLayoutProps {
  children: React.ReactNode;
  currentRoute: string;
  onNavigate: (route: string) => void;
}

interface NavigationItem {
  key: string;
  title: string;
  icon: string;
  activeIcon: string;
}

const navigationItems: NavigationItem[] = [
  { key: 'index', title: 'Dashboard', icon: 'home-outline', activeIcon: 'home' },
  { key: 'scan', title: 'Scan', icon: 'camera-outline', activeIcon: 'camera' },
  { key: 'history', title: 'History', icon: 'time-outline', activeIcon: 'time' },
  { key: 'todo', title: 'Tasks', icon: 'checkmark-circle-outline', activeIcon: 'checkmark-circle' },
  { key: 'profile', title: 'Profile', icon: 'person-outline', activeIcon: 'person' },
];

export const TabletLayout: React.FC<TabletLayoutProps> = ({
  children,
  currentRoute,
  onNavigate,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const layoutConfig = getLayoutConfig();

  if (!isTablet()) {
    // Return children as-is for non-tablet devices
    return <>{children}</>;
  }

  const sidebarWidth = sidebarCollapsed ? 80 : 240;
  const contentWidth = screenWidth - sidebarWidth;

  const renderNavigationItem = (item: NavigationItem) => {
    const isActive = currentRoute === item.key;
    
    return (
      <TouchableOpacity
        key={item.key}
        style={[
          styles.navItem,
          isActive && styles.navItemActive,
          sidebarCollapsed && styles.navItemCollapsed,
        ]}
        onPress={() => onNavigate(item.key)}
      >
        <Ionicons
          name={isActive ? item.activeIcon as any : item.icon as any}
          size={24}
          color={isActive ? Colors.primary : Colors.textSecondary}
        />
        {!sidebarCollapsed && (
          <Text
            style={[
              styles.navItemText,
              isActive && styles.navItemTextActive,
            ]}
          >
            {item.title}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.layout}>
        {/* Sidebar Navigation */}
        <View style={[styles.sidebar, { width: sidebarWidth }]}>
          {/* Header */}
          <View style={styles.sidebarHeader}>
            <View style={styles.logoContainer}>
              <Ionicons name="leaf" size={32} color={Colors.primary} />
              {!sidebarCollapsed && (
                <Text style={styles.logoText}>BioScan</Text>
              )}
            </View>
            <TouchableOpacity
              style={styles.collapseButton}
              onPress={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              <Ionicons
                name={sidebarCollapsed ? 'chevron-forward' : 'chevron-back'}
                size={20}
                color={Colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          {/* Navigation Items */}
          <View style={styles.navigation}>
            {navigationItems.map(renderNavigationItem)}
          </View>

          {/* Footer */}
          <View style={styles.sidebarFooter}>
            <TouchableOpacity
              style={[
                styles.navItem,
                sidebarCollapsed && styles.navItemCollapsed,
              ]}
              onPress={() => onNavigate('settings')}
            >
              <Ionicons
                name="settings-outline"
                size={24}
                color={Colors.textSecondary}
              />
              {!sidebarCollapsed && (
                <Text style={styles.navItemText}>Settings</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Main Content */}
        <View style={[styles.content, { width: contentWidth }]}>
          {children}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    backgroundColor: Colors.backgroundSecondary,
    borderRightWidth: 1,
    borderRightColor: Colors.border,
    paddingVertical: 20,
  },
  sidebarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 12,
  },
  collapseButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  navigation: {
    flex: 1,
    paddingHorizontal: 12,
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 4,
    borderRadius: 12,
  },
  navItemActive: {
    backgroundColor: Colors.primary + '20',
  },
  navItemCollapsed: {
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  navItemText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginLeft: 12,
  },
  navItemTextActive: {
    color: Colors.primary,
    fontWeight: '600',
  },
  sidebarFooter: {
    paddingHorizontal: 12,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  content: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});

// Hook for tablet-specific navigation
export const useTabletNavigation = () => {
  const layoutConfig = getLayoutConfig();
  
  return {
    isTabletLayout: layoutConfig.isTablet,
    useSideNavigation: layoutConfig.useSideNavigation,
    useSplitView: layoutConfig.useSplitView,
    useModalAsFullScreen: layoutConfig.useModalAsFullScreen,
  };
};
