import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';

const { width: screenWidth } = Dimensions.get('window');

interface VoiceSearchBarProps {
  onSearch: (query: string) => void;
  onVoiceStart?: () => void;
  onVoiceEnd?: () => void;
  placeholder?: string;
  style?: any;
}

export const VoiceSearchBar: React.FC<VoiceSearchBarProps> = ({
  onSearch,
  onVoiceStart,
  onVoiceEnd,
  placeholder = "Search or speak...",
  style,
}) => {
  const [isListening, setIsListening] = useState(false);
  const [isTextMode, setIsTextMode] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [transcript, setTranscript] = useState('');
  
  // Animation values
  const waveformAnimation = useRef(new Animated.Value(0)).current;
  const backgroundBlur = useRef(new Animated.Value(0)).current;
  const barScale = useRef(new Animated.Value(1)).current;
  
  // Waveform animation refs
  const wave1 = useRef(new Animated.Value(0.3)).current;
  const wave2 = useRef(new Animated.Value(0.5)).current;
  const wave3 = useRef(new Animated.Value(0.7)).current;
  const wave4 = useRef(new Animated.Value(0.4)).current;
  const wave5 = useRef(new Animated.Value(0.6)).current;

  useEffect(() => {
    if (isListening) {
      startWaveformAnimation();
      startBackgroundBlur();
    } else {
      stopWaveformAnimation();
      stopBackgroundBlur();
    }
  }, [isListening]);

  const startWaveformAnimation = () => {
    const createWaveAnimation = (wave: Animated.Value, duration: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(wave, {
            toValue: 1,
            duration: duration,
            useNativeDriver: false,
          }),
          Animated.timing(wave, {
            toValue: 0.2,
            duration: duration,
            useNativeDriver: false,
          }),
        ])
      );
    };

    Animated.parallel([
      createWaveAnimation(wave1, 800),
      createWaveAnimation(wave2, 1000),
      createWaveAnimation(wave3, 1200),
      createWaveAnimation(wave4, 900),
      createWaveAnimation(wave5, 1100),
    ]).start();
  };

  const stopWaveformAnimation = () => {
    wave1.stopAnimation();
    wave2.stopAnimation();
    wave3.stopAnimation();
    wave4.stopAnimation();
    wave5.stopAnimation();
    
    // Reset to default values
    Animated.parallel([
      Animated.timing(wave1, { toValue: 0.3, duration: 200, useNativeDriver: false }),
      Animated.timing(wave2, { toValue: 0.5, duration: 200, useNativeDriver: false }),
      Animated.timing(wave3, { toValue: 0.7, duration: 200, useNativeDriver: false }),
      Animated.timing(wave4, { toValue: 0.4, duration: 200, useNativeDriver: false }),
      Animated.timing(wave5, { toValue: 0.6, duration: 200, useNativeDriver: false }),
    ]).start();
  };

  const startBackgroundBlur = () => {
    Animated.timing(backgroundBlur, {
      toValue: 1,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const stopBackgroundBlur = () => {
    Animated.timing(backgroundBlur, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const handleMicPress = async () => {
    if (isTextMode) {
      setIsTextMode(false);
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  const startListening = async () => {
    try {
      setIsListening(true);
      setTranscript('');
      onVoiceStart?.();

      // Simulate voice recognition
      // In a real implementation, you would use expo-speech or react-native-voice
      setTimeout(() => {
        const mockTranscripts = [
          'wireless headphones',
          'bluetooth speaker',
          'smartphone case',
          'laptop stand',
          'coffee maker',
        ];
        const randomTranscript = mockTranscripts[Math.floor(Math.random() * mockTranscripts.length)];
        setTranscript(randomTranscript);
        
        setTimeout(() => {
          stopListening();
          onSearch(randomTranscript);
        }, 1000);
      }, 2000);

    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
    }
  };

  const stopListening = () => {
    setIsListening(false);
    onVoiceEnd?.();
  };

  const handleTextSearch = () => {
    if (searchText.trim()) {
      onSearch(searchText.trim());
      setSearchText('');
      setIsTextMode(false);
    }
  };

  const handleTextModeToggle = () => {
    setIsTextMode(true);
  };

  const getWaveformColor = (index: number) => {
    const colors = ['#FF5722', '#FF9800', '#FFC107', '#4CAF50', '#2196F3'];
    return colors[index % colors.length];
  };

  const renderWaveform = () => {
    const waves = [wave1, wave2, wave3, wave4, wave5];
    
    return (
      <View style={styles.waveformContainer}>
        {waves.map((wave, index) => (
          <Animated.View
            key={index}
            style={[
              styles.waveBar,
              {
                height: wave.interpolate({
                  inputRange: [0, 1],
                  outputRange: [8, 32],
                }),
                backgroundColor: getWaveformColor(index),
                opacity: wave.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.6, 1],
                }),
              },
            ]}
          />
        ))}
      </View>
    );
  };

  const renderSearchContent = () => {
    if (isListening) {
      return (
        <View style={styles.listeningContent}>
          <Text style={styles.listeningText}>
            {transcript || 'Listening...'}
          </Text>
          {renderWaveform()}
        </View>
      );
    }

    if (isTextMode) {
      return (
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.textInput}
            value={searchText}
            onChangeText={setSearchText}
            placeholder={placeholder}
            placeholderTextColor={Colors.textLight}
            autoFocus
            onSubmitEditing={handleTextSearch}
            returnKeyType="search"
          />
          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleTextSearch}
          >
            <Ionicons name="send" size={20} color={Colors.primary} />
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={styles.defaultContent}
        onPress={handleTextModeToggle}
        activeOpacity={0.7}
      >
        <Text style={styles.placeholderText}>{placeholder}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <>
      {/* Background Blur Overlay */}
      {isListening && (
        <Animated.View
          style={[
            styles.blurOverlay,
            {
              opacity: backgroundBlur,
            },
          ]}
          pointerEvents="none"
        />
      )}

      {/* Search Bar */}
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ scale: barScale }],
          },
          style,
        ]}
      >
        <View style={styles.searchBar}>
          <TouchableOpacity
            style={styles.micButton}
            onPress={handleMicPress}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isTextMode ? "close" : isListening ? "mic" : "mic-outline"}
              size={24}
              color={isListening ? Colors.error : Colors.textInverse}
            />
          </TouchableOpacity>

          <View style={styles.contentContainer}>
            {renderSearchContent()}
          </View>
        </View>
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 998,
  },
  container: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    zIndex: 999,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  micButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
    minHeight: 24,
    justifyContent: 'center',
  },
  defaultContent: {
    flex: 1,
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: Colors.textInverse,
    opacity: 0.8,
  },
  listeningContent: {
    alignItems: 'center',
  },
  listeningText: {
    fontSize: 16,
    color: Colors.textInverse,
    fontWeight: '600',
    marginBottom: 8,
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
  },
  waveBar: {
    width: 3,
    borderRadius: 1.5,
    marginHorizontal: 2,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.textInverse,
    paddingVertical: 4,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.textInverse,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
});
