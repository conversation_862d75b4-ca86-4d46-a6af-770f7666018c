// BioScan Color Scheme
export const Colors = {
  // Primary colors - Beautiful purple gradient
  primary: '#6366F1', // Indigo
  primaryLight: '#818CF8',
  primaryDark: '#4F46E5',
  
  // Background colors - Clean whites and grays
  background: '#FFFFFF',
  backgroundSecondary: '#F8FAFC',
  backgroundTertiary: '#F1F5F9',
  
  // Accent colors - Vibrant emerald green
  accent: '#10B981', // Emerald
  accentLight: '#34D399',
  accentDark: '#059669',
  
  // Text colors
  text: '#1E293B',
  textSecondary: '#64748B',
  textLight: '#94A3B8',
  textInverse: '#FFFFFF',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Border colors
  border: '#E2E8F0',
  borderLight: '#F1F5F9',
  borderDark: '#CBD5E1',
  
  // Shadow colors
  shadow: '#00000015',
  shadowDark: '#00000025',
  
  // Category specific colors
  food: '#F59E0B', // Amber
  plants: '#10B981', // Emerald
  animals: '#8B5CF6', // Violet
  rocks: '#6B7280', // Gray
  coins: '#F59E0B', // Amber
  
  // Transparent overlays
  overlay: '#00000050',
  overlayLight: '#00000020',
};

export const getColorByCategory = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'food':
      return Colors.food;
    case 'plants':
      return Colors.plants;
    case 'animals':
      return Colors.animals;
    case 'rocks':
    case 'minerals':
      return Colors.rocks;
    case 'coins':
      return Colors.coins;
    default:
      return Colors.primary;
  }
};
