import { IdentificationResult, TodoItem } from '../types';

export class AITaskSuggestionService {
  /**
   * Generate AI-suggested tasks based on scan results
   */
  static generateTasksFromScan(scanResult: IdentificationResult): TodoItem[] {
    const suggestions: TodoItem[] = [];
    const baseId = Date.now().toString();

    switch (scanResult.category) {
      case 'plants':
        suggestions.push(...this.generatePlantCareTasks(scanResult, baseId));
        break;
      case 'food':
        suggestions.push(...this.generateFoodTasks(scanResult, baseId));
        break;
      case 'animals':
        suggestions.push(...this.generateAnimalTasks(scanResult, baseId));
        break;
      case 'rocks':
        suggestions.push(...this.generateRockTasks(scanResult, baseId));
        break;
      case 'coins':
        suggestions.push(...this.generateCoinTasks(scanResult, baseId));
        break;
    }

    return suggestions;
  }

  private static generatePlantCareTasks(scanResult: IdentificationResult, baseId: string): TodoItem[] {
    const tasks: TodoItem[] = [];
    const now = new Date();

    // Watering reminder
    const wateringTask: TodoItem = {
      id: `${baseId}_water`,
      title: `Water your ${scanResult.name}`,
      description: 'Check soil moisture and water if needed',
      completed: false,
      dueDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      dueTime: '09:00',
      priority: 'medium',
      category: 'plant_care',
      relatedScanId: scanResult.id,
      isRecurring: true,
      recurringPattern: 'weekly',
      recurringInterval: 1,
      isAISuggested: true,
      tags: ['watering', 'plant_care'],
      createdAt: now,
      updatedAt: now,
    };

    // Fertilizing reminder
    const fertilizeTask: TodoItem = {
      id: `${baseId}_fertilize`,
      title: `Fertilize your ${scanResult.name}`,
      description: 'Apply balanced liquid fertilizer',
      completed: false,
      dueDate: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
      dueTime: '10:00',
      priority: 'low',
      category: 'plant_care',
      relatedScanId: scanResult.id,
      isRecurring: true,
      recurringPattern: 'monthly',
      recurringInterval: 1,
      isAISuggested: true,
      tags: ['fertilizing', 'plant_care'],
      createdAt: now,
      updatedAt: now,
    };

    // Pruning reminder
    const pruneTask: TodoItem = {
      id: `${baseId}_prune`,
      title: `Check ${scanResult.name} for pruning`,
      description: 'Remove dead or yellowing leaves',
      completed: false,
      dueDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
      dueTime: '11:00',
      priority: 'low',
      category: 'plant_care',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['pruning', 'plant_care'],
      createdAt: now,
      updatedAt: now,
    };

    tasks.push(wateringTask, fertilizeTask, pruneTask);
    return tasks;
  }

  private static generateFoodTasks(scanResult: IdentificationResult, baseId: string): TodoItem[] {
    const tasks: TodoItem[] = [];
    const now = new Date();

    // Expiration check
    const expirationTask: TodoItem = {
      id: `${baseId}_expiry`,
      title: `Check expiration date of ${scanResult.name}`,
      description: 'Verify freshness and consume before expiration',
      completed: false,
      dueDate: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      dueTime: '18:00',
      priority: 'high',
      category: 'food_safety',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['expiration', 'food_safety'],
      createdAt: now,
      updatedAt: now,
    };

    // Recipe suggestion
    const recipeTask: TodoItem = {
      id: `${baseId}_recipe`,
      title: `Find recipes using ${scanResult.name}`,
      description: 'Explore new ways to prepare this ingredient',
      completed: false,
      dueDate: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
      priority: 'low',
      category: 'cooking',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['recipe', 'cooking'],
      createdAt: now,
      updatedAt: now,
    };

    tasks.push(expirationTask, recipeTask);
    return tasks;
  }

  private static generateAnimalTasks(scanResult: IdentificationResult, baseId: string): TodoItem[] {
    const tasks: TodoItem[] = [];
    const now = new Date();

    // Research task
    const researchTask: TodoItem = {
      id: `${baseId}_research`,
      title: `Learn more about ${scanResult.name}`,
      description: 'Research habitat, behavior, and conservation status',
      completed: false,
      dueDate: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000),
      priority: 'medium',
      category: 'research',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['research', 'animals'],
      createdAt: now,
      updatedAt: now,
    };

    tasks.push(researchTask);
    return tasks;
  }

  private static generateRockTasks(scanResult: IdentificationResult, baseId: string): TodoItem[] {
    const tasks: TodoItem[] = [];
    const now = new Date();

    // Research geological properties
    const researchTask: TodoItem = {
      id: `${baseId}_geology`,
      title: `Study geological properties of ${scanResult.name}`,
      description: 'Learn about formation, composition, and uses',
      completed: false,
      dueDate: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000),
      priority: 'low',
      category: 'research',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['geology', 'research'],
      createdAt: now,
      updatedAt: now,
    };

    tasks.push(researchTask);
    return tasks;
  }

  private static generateCoinTasks(scanResult: IdentificationResult, baseId: string): TodoItem[] {
    const tasks: TodoItem[] = [];
    const now = new Date();

    // Value research
    const valueTask: TodoItem = {
      id: `${baseId}_value`,
      title: `Research current value of ${scanResult.name}`,
      description: 'Check recent auction prices and market trends',
      completed: false,
      dueDate: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000),
      priority: 'medium',
      category: 'collecting',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['valuation', 'collecting'],
      createdAt: now,
      updatedAt: now,
    };

    // Authentication check
    const authTask: TodoItem = {
      id: `${baseId}_auth`,
      title: `Verify authenticity of ${scanResult.name}`,
      description: 'Check for signs of counterfeiting or damage',
      completed: false,
      dueDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000),
      priority: 'high',
      category: 'collecting',
      relatedScanId: scanResult.id,
      isAISuggested: true,
      tags: ['authentication', 'collecting'],
      createdAt: now,
      updatedAt: now,
    };

    tasks.push(valueTask, authTask);
    return tasks;
  }

  /**
   * Get task suggestions based on user's scan history patterns
   */
  static getPersonalizedSuggestions(scanHistory: any[], todoItems: TodoItem[]): TodoItem[] {
    const suggestions: TodoItem[] = [];
    const now = new Date();

    // Analyze scan patterns
    const categoryCount = scanHistory.reduce((acc, scan) => {
      acc[scan.result.category] = (acc[scan.result.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Suggest weekly review if user has many scans
    if (scanHistory.length >= 10) {
      const reviewTask: TodoItem = {
        id: `review_${Date.now()}`,
        title: 'Review your scan collection',
        description: 'Organize and review your recent scans',
        completed: false,
        dueDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        priority: 'low',
        category: 'organization',
        isRecurring: true,
        recurringPattern: 'weekly',
        recurringInterval: 1,
        isAISuggested: true,
        tags: ['review', 'organization'],
        createdAt: now,
        updatedAt: now,
      };
      suggestions.push(reviewTask);
    }

    return suggestions;
  }

  /**
   * Get category-specific task templates
   */
  static getTaskTemplates(category: string): Partial<TodoItem>[] {
    const templates: Record<string, Partial<TodoItem>[]> = {
      plant_care: [
        { title: 'Water plants', category: 'plant_care', priority: 'medium', tags: ['watering'] },
        { title: 'Check for pests', category: 'plant_care', priority: 'medium', tags: ['pest_control'] },
        { title: 'Rotate plant position', category: 'plant_care', priority: 'low', tags: ['positioning'] },
      ],
      food_safety: [
        { title: 'Check expiration dates', category: 'food_safety', priority: 'high', tags: ['expiration'] },
        { title: 'Organize pantry', category: 'food_safety', priority: 'low', tags: ['organization'] },
      ],
      research: [
        { title: 'Research identification', category: 'research', priority: 'medium', tags: ['research'] },
        { title: 'Read scientific articles', category: 'research', priority: 'low', tags: ['learning'] },
      ],
    };

    return templates[category] || [];
  }
}
