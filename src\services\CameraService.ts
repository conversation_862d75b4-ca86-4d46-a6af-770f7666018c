import * as ImagePicker from 'expo-image-picker';
import { CameraView } from 'expo-camera';
import { Alert } from 'react-native';

export interface CapturedImage {
  uri: string;
  width: number;
  height: number;
  base64?: string;
}

export class CameraService {
  /**
   * Request camera permissions
   */
  static async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  }

  /**
   * Request media library permissions
   */
  static async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  }

  /**
   * Take a picture using the camera
   */
  static async takePicture(
    cameraRef: React.RefObject<CameraView>,
    options?: {
      quality?: number;
      base64?: boolean;
      skipProcessing?: boolean;
    }
  ): Promise<CapturedImage | null> {
    try {
      if (!cameraRef.current) {
        throw new Error('Camera reference not available');
      }

      const photo = await cameraRef.current.takePictureAsync({
        quality: options?.quality || 0.8,
        base64: options?.base64 || false,
        skipProcessing: options?.skipProcessing || false,
      });

      if (!photo) {
        throw new Error('Failed to capture photo');
      }

      return {
        uri: photo.uri,
        width: photo.width || 0,
        height: photo.height || 0,
        base64: photo.base64,
      };
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Camera Error', 'Failed to capture image. Please try again.');
      return null;
    }
  }

  /**
   * Pick an image from the gallery
   */
  static async pickImageFromGallery(options?: {
    allowsEditing?: boolean;
    aspect?: [number, number];
    quality?: number;
    base64?: boolean;
    allowsMultipleSelection?: boolean;
  }): Promise<CapturedImage | CapturedImage[] | null> {
    try {
      // Check permissions
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'Please grant access to your photo library to select images.'
        );
        return null;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options?.allowsEditing ?? true,
        aspect: options?.aspect || [1, 1],
        quality: options?.quality || 0.8,
        base64: options?.base64 || false,
        allowsMultipleSelection: options?.allowsMultipleSelection || false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const processAsset = (asset: ImagePicker.ImagePickerAsset): CapturedImage => ({
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        base64: asset.base64,
      });

      if (options?.allowsMultipleSelection) {
        return result.assets.map(processAsset);
      } else {
        return processAsset(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image from gallery:', error);
      Alert.alert('Gallery Error', 'Failed to select image. Please try again.');
      return null;
    }
  }

  /**
   * Resize image to optimize for API calls
   */
  static async resizeImage(
    uri: string,
    options?: {
      width?: number;
      height?: number;
      quality?: number;
    }
  ): Promise<string | null> {
    try {
      // For now, we'll return the original URI
      // In a production app, you might want to use expo-image-manipulator
      // to resize images before sending to the API
      return uri;
    } catch (error) {
      console.error('Error resizing image:', error);
      return null;
    }
  }

  /**
   * Convert image to base64 for API calls
   */
  static async imageToBase64(uri: string): Promise<string | null> {
    try {
      // This would typically use expo-file-system to read the file
      // and convert it to base64 for API calls
      console.log('Converting image to base64:', uri);
      return null; // Placeholder
    } catch (error) {
      console.error('Error converting image to base64:', error);
      return null;
    }
  }

  /**
   * Validate image for processing
   */
  static validateImage(image: CapturedImage): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check if image exists
    if (!image.uri) {
      errors.push('Image URI is missing');
    }

    // Check image dimensions
    if (image.width < 100 || image.height < 100) {
      errors.push('Image is too small (minimum 100x100 pixels)');
    }

    // Check aspect ratio (optional)
    if (image.width && image.height) {
      const aspectRatio = image.width / image.height;
      if (aspectRatio < 0.1 || aspectRatio > 10) {
        errors.push('Image aspect ratio is too extreme');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get image metadata
   */
  static getImageMetadata(image: CapturedImage): {
    size: string;
    dimensions: string;
    aspectRatio: number;
  } {
    const aspectRatio = image.width && image.height ? image.width / image.height : 1;
    
    return {
      size: 'Unknown', // Would need expo-file-system to get actual file size
      dimensions: `${image.width || 0} × ${image.height || 0}`,
      aspectRatio: Math.round(aspectRatio * 100) / 100,
    };
  }

  /**
   * Prepare image for identification API
   */
  static async prepareImageForAPI(image: CapturedImage): Promise<{
    uri: string;
    base64?: string;
    metadata: any;
  } | null> {
    try {
      // Validate image
      const validation = this.validateImage(image);
      if (!validation.isValid) {
        Alert.alert('Invalid Image', validation.errors.join('\n'));
        return null;
      }

      // Resize if needed (placeholder)
      const optimizedUri = await this.resizeImage(image.uri, {
        width: 1024,
        height: 1024,
        quality: 0.8,
      });

      if (!optimizedUri) {
        throw new Error('Failed to optimize image');
      }

      // Get metadata
      const metadata = this.getImageMetadata(image);

      return {
        uri: optimizedUri,
        base64: image.base64,
        metadata,
      };
    } catch (error) {
      console.error('Error preparing image for API:', error);
      Alert.alert('Processing Error', 'Failed to process image for identification.');
      return null;
    }
  }
}
