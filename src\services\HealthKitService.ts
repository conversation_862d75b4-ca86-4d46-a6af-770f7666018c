import { Platform } from 'react-native';
import { HealthMetrics, NutritionEntry } from '../types';

export class HealthKitService {
  private static isConnected = false;
  private static platform: 'ios' | 'android' | 'unsupported' = 'unsupported';

  /**
   * Initialize HealthKit connection based on platform
   */
  static async initialize(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios') {
        this.platform = 'ios';
        return await this.initializeAppleHealthKit();
      } else if (Platform.OS === 'android') {
        this.platform = 'android';
        return await this.initializeGoogleFit();
      } else {
        console.log('Health integration not supported on this platform');
        return false;
      }
    } catch (error) {
      console.error('Error initializing health kit:', error);
      return false;
    }
  }

  /**
   * Initialize Apple HealthKit for iOS
   */
  private static async initializeAppleHealthKit(): Promise<boolean> {
    try {
      // In a real implementation, you would use react-native-health or similar
      console.log('Initializing Apple HealthKit...');
      
      const permissions = {
        permissions: {
          read: [
            'Height',
            'Weight',
            'DateOfBirth',
            'BiologicalSex',
            'ActiveEnergyBurned',
            'BasalEnergyBurned',
            'DietaryEnergyConsumed',
            'DietaryProtein',
            'DietaryCarbohydrates',
            'DietaryFiber',
            'DietarySugar',
            'DietaryFatTotal',
            'DietarySodium',
            'DietaryWater',
          ],
          write: [
            'DietaryEnergyConsumed',
            'DietaryProtein',
            'DietaryCarbohydrates',
            'DietaryFiber',
            'DietarySugar',
            'DietaryFatTotal',
            'DietarySodium',
            'DietaryWater',
          ],
        },
      };

      // Mock successful initialization
      this.isConnected = true;
      console.log('Apple HealthKit initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing Apple HealthKit:', error);
      return false;
    }
  }

  /**
   * Initialize Google Fit for Android
   */
  private static async initializeGoogleFit(): Promise<boolean> {
    try {
      console.log('Initializing Google Fit...');
      
      // Mock successful initialization
      this.isConnected = true;
      console.log('Google Fit initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing Google Fit:', error);
      return false;
    }
  }

  /**
   * Request health permissions
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      if (!this.isAvailable()) {
        return false;
      }

      if (this.platform === 'ios') {
        return await this.requestAppleHealthPermissions();
      } else if (this.platform === 'android') {
        return await this.requestGoogleFitPermissions();
      }

      return false;
    } catch (error) {
      console.error('Error requesting health permissions:', error);
      return false;
    }
  }

  /**
   * Request Apple Health permissions
   */
  private static async requestAppleHealthPermissions(): Promise<boolean> {
    try {
      // In a real implementation, this would request actual permissions
      console.log('Requesting Apple Health permissions...');
      return true;
    } catch (error) {
      console.error('Error requesting Apple Health permissions:', error);
      return false;
    }
  }

  /**
   * Request Google Fit permissions
   */
  private static async requestGoogleFitPermissions(): Promise<boolean> {
    try {
      console.log('Requesting Google Fit permissions...');
      return true;
    } catch (error) {
      console.error('Error requesting Google Fit permissions:', error);
      return false;
    }
  }

  /**
   * Write nutrition data to health platform
   */
  static async writeNutritionData(nutritionEntry: NutritionEntry): Promise<boolean> {
    try {
      if (!this.isConnected) {
        throw new Error('Health kit not connected');
      }

      if (this.platform === 'ios') {
        return await this.writeToAppleHealth(nutritionEntry);
      } else if (this.platform === 'android') {
        return await this.writeToGoogleFit(nutritionEntry);
      }

      return false;
    } catch (error) {
      console.error('Error writing nutrition data:', error);
      return false;
    }
  }

  /**
   * Write nutrition data to Apple Health
   */
  private static async writeToAppleHealth(nutritionEntry: NutritionEntry): Promise<boolean> {
    try {
      const healthData = {
        startDate: new Date(nutritionEntry.timestamp),
        endDate: new Date(nutritionEntry.timestamp),
        value: nutritionEntry.nutrients?.calories || 0,
        unit: 'kcal',
      };

      console.log('Writing nutrition data to Apple Health:', healthData);

      // Write individual nutrients
      if (nutritionEntry.nutrients) {
        const nutrientMappings = [
          { key: 'protein', type: 'DietaryProtein', unit: 'g' },
          { key: 'carbs', type: 'DietaryCarbohydrates', unit: 'g' },
          { key: 'fat', type: 'DietaryFatTotal', unit: 'g' },
          { key: 'fiber', type: 'DietaryFiber', unit: 'g' },
          { key: 'sugar', type: 'DietarySugar', unit: 'g' },
          { key: 'sodium', type: 'DietarySodium', unit: 'mg' },
        ];

        for (const mapping of nutrientMappings) {
          if (nutritionEntry.nutrients[mapping.key]) {
            console.log(`Writing ${mapping.type}:`, nutritionEntry.nutrients[mapping.key]);
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error writing to Apple Health:', error);
      return false;
    }
  }

  /**
   * Write nutrition data to Health Connect
   */
  private static async writeToGoogleFit(nutritionEntry: NutritionEntry): Promise<boolean> {
    try {
      const timestamp = new Date(nutritionEntry.timestamp);
      const nutrients = nutritionEntry.nutrients || {};

      console.log('Writing nutrition data to Health Connect:', nutritionEntry);

      // Prepare nutrition records
      const records = [];

      // Add calories record
      if (nutrients.calories) {
        records.push({
          recordType: 'Nutrition',
          startTime: timestamp.toISOString(),
          endTime: timestamp.toISOString(),
          energy: {
            inKilocalories: nutrients.calories,
          },
          name: nutritionEntry.foodItem,
        });
      }

      // Add macronutrient records
      if (nutrients.protein || nutrients.carbs || nutrients.fat) {
        records.push({
          recordType: 'Nutrition',
          startTime: timestamp.toISOString(),
          endTime: timestamp.toISOString(),
          protein: nutrients.protein ? { inGrams: nutrients.protein } : undefined,
          totalCarbohydrate: nutrients.carbs ? { inGrams: nutrients.carbs } : undefined,
          totalFat: nutrients.fat ? { inGrams: nutrients.fat } : undefined,
          dietaryFiber: nutrients.fiber ? { inGrams: nutrients.fiber } : undefined,
          sugar: nutrients.sugar ? { inGrams: nutrients.sugar } : undefined,
          sodium: nutrients.sodium ? { inMilligrams: nutrients.sodium } : undefined,
          name: nutritionEntry.foodItem,
        });
      }

      // Write records to Health Connect
      if (records.length > 0) {
        await writeRecords(records);
        console.log('Successfully wrote nutrition data to Health Connect');
      }

      return true;
    } catch (error) {
      console.error('Error writing to Health Connect:', error);
      return false;
    }
  }

  /**
   * Read health metrics from platform
   */
  static async readHealthMetrics(startDate: Date, endDate: Date): Promise<HealthMetrics> {
    try {
      if (!this.isConnected) {
        throw new Error('Health kit not connected');
      }

      if (this.platform === 'ios') {
        return await this.readFromAppleHealth(startDate, endDate);
      } else if (this.platform === 'android') {
        return await this.readFromGoogleFit(startDate, endDate);
      }

      return this.getEmptyHealthMetrics();
    } catch (error) {
      console.error('Error reading health metrics:', error);
      return this.getEmptyHealthMetrics();
    }
  }

  /**
   * Read health data from Apple Health
   */
  private static async readFromAppleHealth(startDate: Date, endDate: Date): Promise<HealthMetrics> {
    try {
      console.log('Reading health data from Apple Health:', { startDate, endDate });

      // Read dietary energy (calories consumed)
      const caloriesConsumed = await Health.readHealthDataAsync(Health.HealthDataType.DIETARY_ENERGY, {
        startDate,
        endDate,
      });

      // Read active energy burned
      const activeEnergyBurned = await Health.readHealthDataAsync(Health.HealthDataType.ACTIVE_ENERGY_BURNED, {
        startDate,
        endDate,
      });

      // Read basal energy burned
      const basalEnergyBurned = await Health.readHealthDataAsync(Health.HealthDataType.BASAL_ENERGY_BURNED, {
        startDate,
        endDate,
      });

      // Read macronutrients
      const [protein, carbs, fat, fiber] = await Promise.all([
        Health.readHealthDataAsync(Health.HealthDataType.DIETARY_PROTEIN, { startDate, endDate }),
        Health.readHealthDataAsync(Health.HealthDataType.DIETARY_CARBOHYDRATES, { startDate, endDate }),
        Health.readHealthDataAsync(Health.HealthDataType.DIETARY_FAT_TOTAL, { startDate, endDate }),
        Health.readHealthDataAsync(Health.HealthDataType.DIETARY_FIBER, { startDate, endDate }),
      ]);

      // Read weight
      const weightData = await Health.readHealthDataAsync(Health.HealthDataType.BODY_MASS, {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        endDate: new Date(),
      });

      // Calculate totals
      const totalCaloriesConsumed = caloriesConsumed.reduce((sum, entry) => sum + entry.value, 0);
      const totalActiveEnergyBurned = activeEnergyBurned.reduce((sum, entry) => sum + entry.value, 0);
      const totalBasalEnergyBurned = basalEnergyBurned.reduce((sum, entry) => sum + entry.value, 0);
      const totalEnergyBurned = totalActiveEnergyBurned + totalBasalEnergyBurned;

      return {
        calories: {
          consumed: Math.round(totalCaloriesConsumed),
          burned: Math.round(totalEnergyBurned),
          net: Math.round(totalCaloriesConsumed - totalEnergyBurned),
        },
        macronutrients: {
          protein: Math.round(protein.reduce((sum, entry) => sum + entry.value, 0)),
          carbs: Math.round(carbs.reduce((sum, entry) => sum + entry.value, 0)),
          fat: Math.round(fat.reduce((sum, entry) => sum + entry.value, 0)),
          fiber: Math.round(fiber.reduce((sum, entry) => sum + entry.value, 0)),
        },
        activity: {
          steps: 0, // Steps would need a different data type
          activeMinutes: Math.round(totalActiveEnergyBurned / 5), // Rough estimate
          distance: 0, // Distance would need a different data type
        },
        weight: weightData.length > 0 ? weightData[weightData.length - 1].value : undefined,
      };
    } catch (error) {
      console.error('Error reading from Apple Health:', error);
      return this.getEmptyHealthMetrics();
    }
  }

  /**
   * Read health data from Health Connect
   */
  private static async readFromGoogleFit(startDate: Date, endDate: Date): Promise<HealthMetrics> {
    try {
      console.log('Reading health data from Health Connect:', { startDate, endDate });

      // Read nutrition records
      const nutritionRecords = await readRecords('Nutrition', {
        timeRangeFilter: {
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });

      // Read steps records
      const stepsRecords = await readRecords('Steps', {
        timeRangeFilter: {
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });

      // Read calories burned records
      const caloriesBurnedRecords = await readRecords('TotalCaloriesBurned', {
        timeRangeFilter: {
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });

      // Read weight records (last 30 days)
      const weightRecords = await readRecords('Weight', {
        timeRangeFilter: {
          startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date().toISOString(),
        },
      });

      // Calculate nutrition totals
      let totalCaloriesConsumed = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFat = 0;
      let totalFiber = 0;

      nutritionRecords.forEach((record: any) => {
        if (record.energy?.inKilocalories) {
          totalCaloriesConsumed += record.energy.inKilocalories;
        }
        if (record.protein?.inGrams) {
          totalProtein += record.protein.inGrams;
        }
        if (record.totalCarbohydrate?.inGrams) {
          totalCarbs += record.totalCarbohydrate.inGrams;
        }
        if (record.totalFat?.inGrams) {
          totalFat += record.totalFat.inGrams;
        }
        if (record.dietaryFiber?.inGrams) {
          totalFiber += record.dietaryFiber.inGrams;
        }
      });

      // Calculate activity totals
      const totalSteps = stepsRecords.reduce((sum: number, record: any) => sum + (record.count || 0), 0);
      const totalCaloriesBurned = caloriesBurnedRecords.reduce((sum: number, record: any) =>
        sum + (record.energy?.inKilocalories || 0), 0);

      return {
        calories: {
          consumed: Math.round(totalCaloriesConsumed),
          burned: Math.round(totalCaloriesBurned),
          net: Math.round(totalCaloriesConsumed - totalCaloriesBurned),
        },
        macronutrients: {
          protein: Math.round(totalProtein),
          carbs: Math.round(totalCarbs),
          fat: Math.round(totalFat),
          fiber: Math.round(totalFiber),
        },
        activity: {
          steps: totalSteps,
          activeMinutes: Math.round(totalCaloriesBurned / 5), // Rough estimate
          distance: 0, // Would need distance records
        },
        weight: weightRecords.length > 0 ? weightRecords[weightRecords.length - 1].weight?.inKilograms : undefined,
      };
    } catch (error) {
      console.error('Error reading from Health Connect:', error);
      return this.getEmptyHealthMetrics();
    }
  }

  /**
   * Get empty health metrics
   */
  private static getEmptyHealthMetrics(): HealthMetrics {
    return {
      calories: {
        consumed: 0,
        burned: 0,
        net: 0,
      },
      macronutrients: {
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
      },
      activity: {
        steps: 0,
        activeMinutes: 0,
        distance: 0,
      },
    };
  }

  /**
   * Check if health integration is available
   */
  static isAvailable(): boolean {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  /**
   * Check if connected to health platform
   */
  static isHealthKitConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get current platform
   */
  static getCurrentPlatform(): string {
    return this.platform;
  }

  /**
   * Get connection status
   */
  static getConnectionStatus(): {
    available: boolean;
    connected: boolean;
    platform: string;
    permissions: string[];
  } {
    return {
      available: this.isAvailable(),
      connected: this.isConnected,
      platform: this.platform,
      permissions: this.isConnected ? [
        'nutrition.read',
        'nutrition.write',
        'activity.read',
        'body.read',
      ] : [],
    };
  }

  /**
   * Disconnect from health platform
   */
  static async disconnect(): Promise<void> {
    try {
      this.isConnected = false;
      console.log('Disconnected from health platform');
    } catch (error) {
      console.error('Error disconnecting from health platform:', error);
    }
  }
}
