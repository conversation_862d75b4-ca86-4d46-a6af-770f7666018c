import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { TodoItem, NotificationSettings } from '../types';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export class NotificationService {
  /**
   * Request notification permissions
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'BioScan Notifications',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        // Create specific channels for different notification types
        await Notifications.setNotificationChannelAsync('task-reminders', {
          name: 'Task Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
        });

        await Notifications.setNotificationChannelAsync('plant-care', {
          name: 'Plant Care Alerts',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250],
        });

        await Notifications.setNotificationChannelAsync('achievements', {
          name: 'Achievement Unlocks',
          importance: Notifications.AndroidImportance.LOW,
          sound: 'achievement.wav',
        });
      }

      return finalStatus === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * Schedule a notification for a todo item
   */
  static async scheduleTaskReminder(
    todoItem: TodoItem,
    settings: NotificationSettings
  ): Promise<string | null> {
    try {
      if (!settings.enabled || !settings.taskReminders) {
        return null;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      // Check quiet hours
      if (settings.quietHoursEnabled && todoItem.dueTime) {
        const isInQuietHours = this.isInQuietHours(
          todoItem.dueTime,
          settings.quietHoursStart,
          settings.quietHoursEnd
        );
        if (isInQuietHours) {
          // Reschedule to after quiet hours
          todoItem.dueTime = settings.quietHoursEnd;
        }
      }

      const trigger = this.createTriggerFromTodoItem(todoItem);
      if (!trigger) return null;

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Task Reminder',
          body: todoItem.title,
          data: {
            todoId: todoItem.id,
            type: 'task_reminder',
            category: todoItem.category,
          },
          categoryIdentifier: 'task-reminder',
        },
        trigger,
      });

      // Schedule recurring notifications if needed
      if (todoItem.isRecurring && todoItem.recurringPattern) {
        await this.scheduleRecurringReminders(todoItem, settings);
      }

      return notificationId;
    } catch (error) {
      console.error('Error scheduling task reminder:', error);
      return null;
    }
  }

  /**
   * Schedule plant care alert
   */
  static async schedulePlantCareAlert(
    plantName: string,
    careType: string,
    dueDate: Date,
    settings: NotificationSettings
  ): Promise<string | null> {
    try {
      if (!settings.enabled || !settings.plantCareAlerts) {
        return null;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return null;

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: `🌱 Plant Care Reminder`,
          body: `Time to ${careType.toLowerCase()} your ${plantName}`,
          data: {
            type: 'plant_care',
            plantName,
            careType,
          },
        },
        trigger: {
          date: dueDate,
          channelId: 'plant-care',
        },
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling plant care alert:', error);
      return null;
    }
  }

  /**
   * Send achievement unlock notification
   */
  static async sendAchievementNotification(
    achievementTitle: string,
    achievementDescription: string,
    settings: NotificationSettings
  ): Promise<void> {
    try {
      if (!settings.enabled || !settings.achievementUnlocks) {
        return;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return;

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🏆 Achievement Unlocked!',
          body: `${achievementTitle}: ${achievementDescription}`,
          data: {
            type: 'achievement',
            title: achievementTitle,
          },
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending achievement notification:', error);
    }
  }

  /**
   * Schedule weekly summary notification
   */
  static async scheduleWeeklySummary(
    scanCount: number,
    completedTasks: number,
    settings: NotificationSettings
  ): Promise<string | null> {
    try {
      if (!settings.enabled || !settings.weeklySummaries) {
        return null;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return null;

      // Schedule for Sunday at 6 PM
      const nextSunday = new Date();
      nextSunday.setDate(nextSunday.getDate() + (7 - nextSunday.getDay()));
      nextSunday.setHours(18, 0, 0, 0);

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '📊 Weekly BioScan Summary',
          body: `This week: ${scanCount} scans completed, ${completedTasks} tasks finished!`,
          data: {
            type: 'weekly_summary',
            scanCount,
            completedTasks,
          },
        },
        trigger: {
          date: nextSunday,
          repeats: true,
        },
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling weekly summary:', error);
      return null;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  static async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  /**
   * Cancel all notifications
   */
  static async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  /**
   * Get all scheduled notifications
   */
  static async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Create trigger from todo item
   */
  private static createTriggerFromTodoItem(todoItem: TodoItem): Notifications.NotificationTriggerInput | null {
    if (!todoItem.dueDate) return null;

    const triggerDate = new Date(todoItem.dueDate);
    
    if (todoItem.dueTime) {
      const [hours, minutes] = todoItem.dueTime.split(':').map(Number);
      triggerDate.setHours(hours, minutes, 0, 0);
    }

    return {
      date: triggerDate,
      repeats: todoItem.isRecurring || false,
    };
  }

  /**
   * Schedule recurring reminders
   */
  private static async scheduleRecurringReminders(
    todoItem: TodoItem,
    settings: NotificationSettings
  ): Promise<void> {
    if (!todoItem.recurringPattern || !todoItem.dueDate) return;

    const interval = todoItem.recurringInterval || 1;
    let nextDate = new Date(todoItem.dueDate);

    // Schedule next 5 occurrences
    for (let i = 0; i < 5; i++) {
      switch (todoItem.recurringPattern) {
        case 'daily':
          nextDate.setDate(nextDate.getDate() + interval);
          break;
        case 'weekly':
          nextDate.setDate(nextDate.getDate() + (7 * interval));
          break;
        case 'monthly':
          nextDate.setMonth(nextDate.getMonth() + interval);
          break;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Recurring Task Reminder',
          body: todoItem.title,
          data: {
            todoId: todoItem.id,
            type: 'recurring_task',
            occurrence: i + 1,
          },
        },
        trigger: {
          date: new Date(nextDate),
        },
      });
    }
  }

  /**
   * Check if time is in quiet hours
   */
  private static isInQuietHours(
    time: string,
    quietStart: string,
    quietEnd: string
  ): boolean {
    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(quietStart);
    const endMinutes = this.timeToMinutes(quietEnd);

    if (startMinutes <= endMinutes) {
      return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
    } else {
      // Quiet hours span midnight
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }
  }

  /**
   * Convert HH:MM time to minutes since midnight
   */
  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
