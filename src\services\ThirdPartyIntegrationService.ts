import { Linking, Alert } from 'react-native';
import { TodoItem, ThirdPartyApp, IntegrationConfig } from '../types';

export class ThirdPartyIntegrationService {
  private static connectedApps: Map<string, ThirdPartyApp> = new Map();
  private static integrationConfigs: Map<string, IntegrationConfig> = new Map();

  /**
   * Initialize third-party integrations
   */
  static async initialize(): Promise<void> {
    try {
      // Load saved integrations
      await this.loadSavedIntegrations();
      
      // Setup supported apps
      this.setupSupportedApps();
      
      console.log('Third-party integrations initialized');
    } catch (error) {
      console.error('Error initializing third-party integrations:', error);
    }
  }

  /**
   * Get list of supported third-party apps
   */
  static getSupportedApps(): ThirdPartyApp[] {
    return [
      {
        id: 'todoist',
        name: 'Todoist',
        category: 'productivity',
        icon: 'checkmark-circle',
        description: 'Sync tasks with Todoist',
        urlScheme: 'todoist://',
        webUrl: 'https://todoist.com',
        apiEndpoint: 'https://api.todoist.com/rest/v2',
        requiresAuth: true,
        features: ['tasks', 'projects', 'labels'],
      },
      {
        id: 'notion',
        name: 'Notion',
        category: 'productivity',
        icon: 'document-text',
        description: 'Create pages and databases in Notion',
        urlScheme: 'notion://',
        webUrl: 'https://notion.so',
        apiEndpoint: 'https://api.notion.com/v1',
        requiresAuth: true,
        features: ['pages', 'databases', 'blocks'],
      },
      {
        id: 'trello',
        name: 'Trello',
        category: 'productivity',
        icon: 'grid',
        description: 'Add cards to Trello boards',
        urlScheme: 'trello://',
        webUrl: 'https://trello.com',
        apiEndpoint: 'https://api.trello.com/1',
        requiresAuth: true,
        features: ['boards', 'lists', 'cards'],
      },
      {
        id: 'evernote',
        name: 'Evernote',
        category: 'notes',
        icon: 'document',
        description: 'Save scan results to Evernote',
        urlScheme: 'evernote://',
        webUrl: 'https://evernote.com',
        apiEndpoint: 'https://api.evernote.com',
        requiresAuth: true,
        features: ['notes', 'notebooks', 'tags'],
      },
      {
        id: 'slack',
        name: 'Slack',
        category: 'communication',
        icon: 'chatbubbles',
        description: 'Send scan results to Slack channels',
        urlScheme: 'slack://',
        webUrl: 'https://slack.com',
        apiEndpoint: 'https://slack.com/api',
        requiresAuth: true,
        features: ['messages', 'channels', 'files'],
      },
      {
        id: 'calendar',
        name: 'Calendar',
        category: 'productivity',
        icon: 'calendar',
        description: 'Add task reminders to calendar',
        urlScheme: 'calshow://',
        webUrl: '',
        requiresAuth: false,
        features: ['events', 'reminders'],
      },
    ];
  }

  /**
   * Connect to a third-party app
   */
  static async connectApp(appId: string): Promise<boolean> {
    try {
      const app = this.getSupportedApps().find(a => a.id === appId);
      if (!app) {
        throw new Error(`Unsupported app: ${appId}`);
      }

      if (app.requiresAuth) {
        // In a real implementation, you would handle OAuth flow
        const authResult = await this.handleOAuthFlow(app);
        if (!authResult.success) {
          return false;
        }
        app.accessToken = authResult.accessToken;
      }

      // Test connection
      const connectionTest = await this.testConnection(app);
      if (!connectionTest) {
        throw new Error(`Failed to connect to ${app.name}`);
      }

      this.connectedApps.set(appId, app);
      await this.saveIntegrations();
      
      console.log(`Successfully connected to ${app.name}`);
      return true;
    } catch (error) {
      console.error(`Error connecting to app ${appId}:`, error);
      return false;
    }
  }

  /**
   * Disconnect from a third-party app
   */
  static async disconnectApp(appId: string): Promise<void> {
    try {
      this.connectedApps.delete(appId);
      this.integrationConfigs.delete(appId);
      await this.saveIntegrations();
      
      console.log(`Disconnected from app: ${appId}`);
    } catch (error) {
      console.error(`Error disconnecting from app ${appId}:`, error);
    }
  }

  /**
   * Sync task to connected productivity apps
   */
  static async syncTaskToApps(task: TodoItem): Promise<{
    success: string[];
    failed: string[];
  }> {
    const success: string[] = [];
    const failed: string[] = [];

    const productivityApps = Array.from(this.connectedApps.values())
      .filter(app => app.category === 'productivity' && app.features.includes('tasks'));

    for (const app of productivityApps) {
      try {
        const result = await this.syncTaskToApp(task, app);
        if (result) {
          success.push(app.name);
        } else {
          failed.push(app.name);
        }
      } catch (error) {
        console.error(`Error syncing task to ${app.name}:`, error);
        failed.push(app.name);
      }
    }

    return { success, failed };
  }

  /**
   * Sync task to specific app
   */
  private static async syncTaskToApp(task: TodoItem, app: ThirdPartyApp): Promise<boolean> {
    try {
      switch (app.id) {
        case 'todoist':
          return await this.syncToTodoist(task, app);
        case 'notion':
          return await this.syncToNotion(task, app);
        case 'trello':
          return await this.syncToTrello(task, app);
        case 'calendar':
          return await this.syncToCalendar(task);
        default:
          console.log(`Sync not implemented for ${app.name}`);
          return false;
      }
    } catch (error) {
      console.error(`Error syncing to ${app.name}:`, error);
      return false;
    }
  }

  /**
   * Sync task to Todoist
   */
  private static async syncToTodoist(task: TodoItem, app: ThirdPartyApp): Promise<boolean> {
    try {
      const todoistTask = {
        content: task.title,
        description: task.description || '',
        due_date: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : undefined,
        priority: this.mapPriorityToTodoist(task.priority),
        labels: task.tags || [],
      };

      console.log('Syncing task to Todoist:', todoistTask);
      
      // In a real implementation, you would make an API call
      // const response = await fetch(`${app.apiEndpoint}/tasks`, {
      //   method: 'POST',
      //   headers: {
      //     'Authorization': `Bearer ${app.accessToken}`,
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(todoistTask),
      // });
      
      return true;
    } catch (error) {
      console.error('Error syncing to Todoist:', error);
      return false;
    }
  }

  /**
   * Sync task to Notion
   */
  private static async syncToNotion(task: TodoItem, app: ThirdPartyApp): Promise<boolean> {
    try {
      const notionPage = {
        parent: { database_id: 'your_database_id' },
        properties: {
          Name: {
            title: [{ text: { content: task.title } }]
          },
          Status: {
            select: { name: task.completed ? 'Done' : 'To Do' }
          },
          Priority: {
            select: { name: task.priority }
          },
          'Due Date': task.dueDate ? {
            date: { start: new Date(task.dueDate).toISOString().split('T')[0] }
          } : undefined,
        },
      };

      console.log('Syncing task to Notion:', notionPage);
      return true;
    } catch (error) {
      console.error('Error syncing to Notion:', error);
      return false;
    }
  }

  /**
   * Sync task to Trello
   */
  private static async syncToTrello(task: TodoItem, app: ThirdPartyApp): Promise<boolean> {
    try {
      const trelloCard = {
        name: task.title,
        desc: task.description || '',
        due: task.dueDate ? new Date(task.dueDate).toISOString() : undefined,
        idList: 'your_list_id',
      };

      console.log('Syncing task to Trello:', trelloCard);
      return true;
    } catch (error) {
      console.error('Error syncing to Trello:', error);
      return false;
    }
  }

  /**
   * Sync task to Calendar
   */
  private static async syncToCalendar(task: TodoItem): Promise<boolean> {
    try {
      if (!task.dueDate) {
        console.log('Task has no due date, skipping calendar sync');
        return false;
      }

      const calendarUrl = `calshow:${new Date(task.dueDate).getTime()}`;
      const canOpen = await Linking.canOpenURL(calendarUrl);
      
      if (canOpen) {
        await Linking.openURL(calendarUrl);
        return true;
      } else {
        // Fallback to system calendar
        const eventUrl = `data:text/calendar;charset=utf8,BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
DTSTART:${new Date(task.dueDate).toISOString().replace(/[-:]/g, '').split('.')[0]}Z
SUMMARY:${task.title}
DESCRIPTION:${task.description || ''}
END:VEVENT
END:VCALENDAR`;
        
        await Linking.openURL(eventUrl);
        return true;
      }
    } catch (error) {
      console.error('Error syncing to Calendar:', error);
      return false;
    }
  }

  /**
   * Get connected apps
   */
  static getConnectedApps(): ThirdPartyApp[] {
    return Array.from(this.connectedApps.values());
  }

  /**
   * Check if app is connected
   */
  static isAppConnected(appId: string): boolean {
    return this.connectedApps.has(appId);
  }

  /**
   * Handle OAuth flow (mock implementation)
   */
  private static async handleOAuthFlow(app: ThirdPartyApp): Promise<{
    success: boolean;
    accessToken?: string;
  }> {
    try {
      // In a real implementation, you would:
      // 1. Open OAuth URL in browser
      // 2. Handle redirect back to app
      // 3. Exchange code for access token
      
      console.log(`Starting OAuth flow for ${app.name}`);
      
      // Mock successful authentication
      return {
        success: true,
        accessToken: `mock_token_${app.id}_${Date.now()}`,
      };
    } catch (error) {
      console.error(`OAuth flow failed for ${app.name}:`, error);
      return { success: false };
    }
  }

  /**
   * Test connection to app
   */
  private static async testConnection(app: ThirdPartyApp): Promise<boolean> {
    try {
      // In a real implementation, you would make a test API call
      console.log(`Testing connection to ${app.name}`);
      return true;
    } catch (error) {
      console.error(`Connection test failed for ${app.name}:`, error);
      return false;
    }
  }

  /**
   * Map priority to Todoist format
   */
  private static mapPriorityToTodoist(priority: string): number {
    switch (priority) {
      case 'high': return 4;
      case 'medium': return 3;
      case 'low': return 2;
      default: return 1;
    }
  }

  /**
   * Setup supported apps configuration
   */
  private static setupSupportedApps(): void {
    // Initialize default configurations for supported apps
    this.getSupportedApps().forEach(app => {
      if (!this.integrationConfigs.has(app.id)) {
        this.integrationConfigs.set(app.id, {
          appId: app.id,
          enabled: false,
          autoSync: false,
          syncSettings: {
            syncTasks: true,
            syncScans: false,
            syncAchievements: false,
          },
        });
      }
    });
  }

  /**
   * Load saved integrations (mock implementation)
   */
  private static async loadSavedIntegrations(): Promise<void> {
    try {
      // In a real implementation, you would load from AsyncStorage
      console.log('Loading saved integrations...');
    } catch (error) {
      console.error('Error loading saved integrations:', error);
    }
  }

  /**
   * Save integrations (mock implementation)
   */
  private static async saveIntegrations(): Promise<void> {
    try {
      // In a real implementation, you would save to AsyncStorage
      console.log('Saving integrations...');
    } catch (error) {
      console.error('Error saving integrations:', error);
    }
  }
}
