import { Linking, Platform } from 'react-native';
import { ScanHistory, TodoItem } from '../types';

export class VoiceAssistantService {
  /**
   * Register voice shortcuts for iOS Siri
   */
  static async registerSiriShortcuts(): Promise<void> {
    if (Platform.OS !== 'ios') return;

    try {
      // In a real implementation, you would use react-native-siri-shortcut
      // or expo-intent-launcher for this functionality
      
      const shortcuts = [
        {
          activityType: 'com.bioscan.scan',
          title: 'Scan with BioScan',
          userInfo: { action: 'scan' },
          keywords: ['scan', 'identify', 'bioscan'],
          persistentIdentifier: 'scan-shortcut',
          isEligibleForSearch: true,
          isEligibleForPrediction: true,
        },
        {
          activityType: 'com.bioscan.addTask',
          title: 'Add Task to BioScan',
          userInfo: { action: 'addTask' },
          keywords: ['task', 'todo', 'reminder', 'bioscan'],
          persistentIdentifier: 'add-task-shortcut',
          isEligibleForSearch: true,
          isEligibleForPrediction: true,
        },
        {
          activityType: 'com.bioscan.viewHistory',
          title: 'View BioScan History',
          userInfo: { action: 'viewHistory' },
          keywords: ['history', 'scans', 'bioscan'],
          persistentIdentifier: 'view-history-shortcut',
          isEligibleForSearch: true,
          isEligibleForPrediction: true,
        },
      ];

      // Register shortcuts with iOS
      console.log('Siri shortcuts registered:', shortcuts.length);
    } catch (error) {
      console.error('Error registering Siri shortcuts:', error);
    }
  }

  /**
   * Handle voice assistant intents
   */
  static async handleVoiceIntent(intent: string, parameters?: any): Promise<void> {
    try {
      switch (intent) {
        case 'scan':
          await this.handleScanIntent(parameters);
          break;
        case 'addTask':
          await this.handleAddTaskIntent(parameters);
          break;
        case 'viewHistory':
          await this.handleViewHistoryIntent(parameters);
          break;
        case 'nutritionLog':
          await this.handleNutritionLogIntent(parameters);
          break;
        default:
          console.log('Unknown voice intent:', intent);
      }
    } catch (error) {
      console.error('Error handling voice intent:', error);
    }
  }

  /**
   * Handle scan intent from voice assistant
   */
  private static async handleScanIntent(parameters?: any): Promise<void> {
    // Open the app to scan screen
    const url = 'bioscan://scan';
    
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        // Fallback to opening the app
        await Linking.openURL('bioscan://');
      }
    } catch (error) {
      console.error('Error opening scan screen:', error);
    }
  }

  /**
   * Handle add task intent from voice assistant
   */
  private static async handleAddTaskIntent(parameters?: any): Promise<void> {
    const taskTitle = parameters?.title || parameters?.text || 'New task from voice';
    const url = `bioscan://addTask?title=${encodeURIComponent(taskTitle)}`;
    
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        await Linking.openURL('bioscan://');
      }
    } catch (error) {
      console.error('Error opening add task screen:', error);
    }
  }

  /**
   * Handle view history intent from voice assistant
   */
  private static async handleViewHistoryIntent(parameters?: any): Promise<void> {
    const category = parameters?.category;
    const url = category 
      ? `bioscan://history?category=${encodeURIComponent(category)}`
      : 'bioscan://history';
    
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        await Linking.openURL('bioscan://');
      }
    } catch (error) {
      console.error('Error opening history screen:', error);
    }
  }

  /**
   * Handle nutrition logging intent
   */
  private static async handleNutritionLogIntent(parameters?: any): Promise<void> {
    const foodItem = parameters?.food || parameters?.item;
    const quantity = parameters?.quantity || parameters?.amount;
    
    const url = `bioscan://nutritionLog?food=${encodeURIComponent(foodItem || '')}&quantity=${encodeURIComponent(quantity || '')}`;
    
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        await Linking.openURL('bioscan://');
      }
    } catch (error) {
      console.error('Error opening nutrition log:', error);
    }
  }

  /**
   * Generate voice response for scan results
   */
  static generateScanResponse(scanResult: ScanHistory): string {
    const { name, category, confidence } = scanResult.result;
    const confidencePercent = Math.round(confidence * 100);
    
    return `I identified this as ${name}, which is a ${category}. I'm ${confidencePercent}% confident in this identification.`;
  }

  /**
   * Generate voice response for task creation
   */
  static generateTaskResponse(task: TodoItem): string {
    const dueText = task.dueDate 
      ? ` due ${new Date(task.dueDate).toLocaleDateString()}`
      : '';
    
    return `I've added "${task.title}" to your task list${dueText}.`;
  }

  /**
   * Generate voice response for history queries
   */
  static generateHistoryResponse(scans: ScanHistory[], category?: string): string {
    if (scans.length === 0) {
      return category 
        ? `You haven't scanned any ${category} items yet.`
        : `You haven't scanned anything yet.`;
    }

    const categoryText = category ? ` ${category}` : '';
    const recentScan = scans[0];
    
    if (scans.length === 1) {
      return `You have 1${categoryText} scan: ${recentScan.result.name}.`;
    }

    return `You have ${scans.length}${categoryText} scans. Your most recent was ${recentScan.result.name}.`;
  }

  /**
   * Setup Google Assistant Actions (Android)
   */
  static async setupGoogleAssistantActions(): Promise<void> {
    if (Platform.OS !== 'android') return;

    try {
      // In a real implementation, you would use Google Assistant SDK
      // or Actions on Google to register these intents
      
      const actions = [
        {
          name: 'scan_item',
          trigger: ['scan this', 'identify this', 'what is this'],
          action: 'com.bioscan.SCAN',
        },
        {
          name: 'add_task',
          trigger: ['add task', 'create reminder', 'remember to'],
          action: 'com.bioscan.ADD_TASK',
        },
        {
          name: 'view_history',
          trigger: ['show my scans', 'view history', 'what did I scan'],
          action: 'com.bioscan.VIEW_HISTORY',
        },
        {
          name: 'log_nutrition',
          trigger: ['log food', 'track nutrition', 'I ate'],
          action: 'com.bioscan.LOG_NUTRITION',
        },
      ];

      console.log('Google Assistant actions configured:', actions.length);
    } catch (error) {
      console.error('Error setting up Google Assistant actions:', error);
    }
  }

  /**
   * Process natural language commands
   */
  static processNaturalLanguageCommand(command: string): {
    intent: string;
    parameters: any;
  } | null {
    const lowerCommand = command.toLowerCase();

    // Scan intents
    if (lowerCommand.includes('scan') || lowerCommand.includes('identify') || lowerCommand.includes('what is this')) {
      return { intent: 'scan', parameters: {} };
    }

    // Task intents
    if (lowerCommand.includes('add task') || lowerCommand.includes('remind me') || lowerCommand.includes('create task')) {
      const taskMatch = lowerCommand.match(/(?:add task|remind me to|create task)\s+(.+)/);
      const title = taskMatch ? taskMatch[1] : 'New task';
      return { intent: 'addTask', parameters: { title } };
    }

    // History intents
    if (lowerCommand.includes('history') || lowerCommand.includes('scans') || lowerCommand.includes('what did i scan')) {
      const categoryMatch = lowerCommand.match(/(?:show|view)\s+(?:my\s+)?(\w+)\s+(?:scans|history)/);
      const category = categoryMatch ? categoryMatch[1] : undefined;
      return { intent: 'viewHistory', parameters: { category } };
    }

    // Nutrition intents
    if (lowerCommand.includes('i ate') || lowerCommand.includes('log food') || lowerCommand.includes('track nutrition')) {
      const foodMatch = lowerCommand.match(/(?:i ate|log food|track)\s+(.+?)(?:\s+(\d+(?:\.\d+)?)\s*(\w+))?$/);
      if (foodMatch) {
        const food = foodMatch[1];
        const quantity = foodMatch[2];
        const unit = foodMatch[3];
        return { 
          intent: 'nutritionLog', 
          parameters: { food, quantity, unit } 
        };
      }
    }

    return null;
  }

  /**
   * Enable voice assistant integration
   */
  static async enableVoiceIntegration(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios') {
        await this.registerSiriShortcuts();
      } else if (Platform.OS === 'android') {
        await this.setupGoogleAssistantActions();
      }

      return true;
    } catch (error) {
      console.error('Error enabling voice integration:', error);
      return false;
    }
  }

  /**
   * Check if voice integration is available
   */
  static isVoiceIntegrationAvailable(): boolean {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  /**
   * Get voice integration status
   */
  static getVoiceIntegrationStatus(): {
    available: boolean;
    platform: string;
    features: string[];
  } {
    const available = this.isVoiceIntegrationAvailable();
    const platform = Platform.OS;

    const features = [];
    if (platform === 'ios') {
      features.push('Siri Shortcuts', 'Voice Commands', 'App Intents');
    } else if (platform === 'android') {
      features.push('Google Assistant', 'Voice Actions', 'App Shortcuts');
    }

    return { available, platform, features };
  }
}
