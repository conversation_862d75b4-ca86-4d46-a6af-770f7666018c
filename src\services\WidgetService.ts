import AsyncStorage from '@react-native-async-storage/async-storage';
import { ScanHistory, TodoItem, WidgetData, WidgetConfig } from '../types';

export class WidgetService {
  private static readonly WIDGET_DATA_KEY = 'widget_data';
  private static readonly WIDGET_CONFIG_KEY = 'widget_config';

  /**
   * Get widget data for home screen widgets
   */
  static async getWidgetData(): Promise<WidgetData> {
    try {
      const data = await AsyncStorage.getItem(this.WIDGET_DATA_KEY);
      return data ? JSON.parse(data) : this.getDefaultWidgetData();
    } catch (error) {
      console.error('Error getting widget data:', error);
      return this.getDefaultWidgetData();
    }
  }

  /**
   * Update widget data
   */
  static async updateWidgetData(
    scanHistory: ScanHistory[],
    todoItems: TodoItem[]
  ): Promise<void> {
    try {
      const widgetData: WidgetData = {
        lastUpdated: new Date().toISOString(),
        stats: {
          totalScans: scanHistory.length,
          recentScansCount: this.getRecentScansCount(scanHistory),
          pendingTasks: todoItems.filter(item => !item.completed).length,
          completedTasks: todoItems.filter(item => item.completed).length,
        },
        recentScans: this.getRecentScansForWidget(scanHistory),
        upcomingTasks: this.getUpcomingTasksForWidget(todoItems),
        achievements: this.getAchievementsForWidget(scanHistory, todoItems),
        quickActions: this.getQuickActions(),
      };

      await AsyncStorage.setItem(this.WIDGET_DATA_KEY, JSON.stringify(widgetData));
      
      // Notify platform-specific widget implementations
      this.notifyWidgetUpdate(widgetData);
    } catch (error) {
      console.error('Error updating widget data:', error);
    }
  }

  /**
   * Get widget configuration
   */
  static async getWidgetConfig(): Promise<WidgetConfig> {
    try {
      const config = await AsyncStorage.getItem(this.WIDGET_CONFIG_KEY);
      return config ? JSON.parse(config) : this.getDefaultWidgetConfig();
    } catch (error) {
      console.error('Error getting widget config:', error);
      return this.getDefaultWidgetConfig();
    }
  }

  /**
   * Update widget configuration
   */
  static async updateWidgetConfig(config: Partial<WidgetConfig>): Promise<void> {
    try {
      const currentConfig = await this.getWidgetConfig();
      const updatedConfig = { ...currentConfig, ...config };
      await AsyncStorage.setItem(this.WIDGET_CONFIG_KEY, JSON.stringify(updatedConfig));
    } catch (error) {
      console.error('Error updating widget config:', error);
    }
  }

  /**
   * Get default widget data
   */
  private static getDefaultWidgetData(): WidgetData {
    return {
      lastUpdated: new Date().toISOString(),
      stats: {
        totalScans: 0,
        recentScansCount: 0,
        pendingTasks: 0,
        completedTasks: 0,
      },
      recentScans: [],
      upcomingTasks: [],
      achievements: [],
      quickActions: this.getQuickActions(),
    };
  }

  /**
   * Get default widget configuration
   */
  private static getDefaultWidgetConfig(): WidgetConfig {
    return {
      showStats: true,
      showRecentScans: true,
      showUpcomingTasks: true,
      showAchievements: false,
      showQuickActions: true,
      maxRecentScans: 3,
      maxUpcomingTasks: 3,
      refreshInterval: 30, // minutes
      theme: 'auto',
    };
  }

  /**
   * Get recent scans count (last 7 days)
   */
  private static getRecentScansCount(scanHistory: ScanHistory[]): number {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return scanHistory.filter(scan => 
      new Date(scan.result.timestamp) > sevenDaysAgo
    ).length;
  }

  /**
   * Get recent scans for widget display
   */
  private static getRecentScansForWidget(scanHistory: ScanHistory[]): Array<{
    id: string;
    name: string;
    category: string;
    timestamp: string;
  }> {
    return scanHistory
      .sort((a, b) => new Date(b.result.timestamp).getTime() - new Date(a.result.timestamp).getTime())
      .slice(0, 3)
      .map(scan => ({
        id: scan.id,
        name: scan.result.name,
        category: scan.result.category,
        timestamp: scan.result.timestamp,
      }));
  }

  /**
   * Get upcoming tasks for widget display
   */
  private static getUpcomingTasksForWidget(todoItems: TodoItem[]): Array<{
    id: string;
    title: string;
    dueDate?: string;
    priority: string;
  }> {
    const now = new Date();
    const upcoming = todoItems
      .filter(item => !item.completed)
      .filter(item => {
        if (!item.dueDate) return true;
        return new Date(item.dueDate) >= now;
      })
      .sort((a, b) => {
        if (!a.dueDate && !b.dueDate) return 0;
        if (!a.dueDate) return 1;
        if (!b.dueDate) return -1;
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      })
      .slice(0, 3);

    return upcoming.map(task => ({
      id: task.id,
      title: task.title,
      dueDate: task.dueDate,
      priority: task.priority,
    }));
  }

  /**
   * Get achievements for widget display
   */
  private static getAchievementsForWidget(
    scanHistory: ScanHistory[],
    todoItems: TodoItem[]
  ): Array<{
    title: string;
    description: string;
    unlocked: boolean;
  }> {
    const completedTasks = todoItems.filter(item => item.completed).length;
    const totalScans = scanHistory.length;
    
    return [
      {
        title: 'First Scan',
        description: 'Completed your first scan',
        unlocked: totalScans >= 1,
      },
      {
        title: 'Explorer',
        description: 'Scanned 10 different items',
        unlocked: totalScans >= 10,
      },
      {
        title: 'Task Master',
        description: 'Completed 5 tasks',
        unlocked: completedTasks >= 5,
      },
    ];
  }

  /**
   * Get quick actions for widget
   */
  private static getQuickActions(): Array<{
    id: string;
    title: string;
    icon: string;
    action: string;
  }> {
    return [
      {
        id: 'scan',
        title: 'Quick Scan',
        icon: 'camera',
        action: 'open_scan',
      },
      {
        id: 'add_task',
        title: 'Add Task',
        icon: 'add',
        action: 'open_add_task',
      },
      {
        id: 'view_history',
        title: 'View History',
        icon: 'time',
        action: 'open_history',
      },
    ];
  }

  /**
   * Notify platform-specific widget implementations
   */
  private static notifyWidgetUpdate(widgetData: WidgetData): void {
    // For iOS: Use WidgetKit timeline updates
    // For Android: Use App Widget updates
    // This would be implemented with platform-specific native modules
    
    console.log('Widget data updated:', {
      timestamp: widgetData.lastUpdated,
      stats: widgetData.stats,
    });
  }

  /**
   * Generate widget preview data for configuration
   */
  static generatePreviewData(): WidgetData {
    return {
      lastUpdated: new Date().toISOString(),
      stats: {
        totalScans: 42,
        recentScansCount: 7,
        pendingTasks: 3,
        completedTasks: 15,
      },
      recentScans: [
        {
          id: '1',
          name: 'Red Rose',
          category: 'plants',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Golden Retriever',
          category: 'animals',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
        },
        {
          id: '3',
          name: 'Apple',
          category: 'food',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
        },
      ],
      upcomingTasks: [
        {
          id: '1',
          title: 'Water the plants',
          dueDate: new Date(Date.now() + 86400000).toISOString(),
          priority: 'high',
        },
        {
          id: '2',
          title: 'Research butterfly species',
          priority: 'medium',
        },
      ],
      achievements: [
        {
          title: 'Explorer',
          description: 'Scanned 10 different items',
          unlocked: true,
        },
        {
          title: 'Task Master',
          description: 'Completed 5 tasks',
          unlocked: true,
        },
      ],
      quickActions: this.getQuickActions(),
    };
  }

  /**
   * Format data for specific widget sizes
   */
  static formatForWidgetSize(
    data: WidgetData,
    size: 'small' | 'medium' | 'large'
  ): Partial<WidgetData> {
    switch (size) {
      case 'small':
        return {
          stats: data.stats,
          quickActions: data.quickActions.slice(0, 1),
        };
      case 'medium':
        return {
          stats: data.stats,
          recentScans: data.recentScans.slice(0, 2),
          quickActions: data.quickActions.slice(0, 2),
        };
      case 'large':
        return data;
      default:
        return data;
    }
  }
}
