import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { AITaskSuggestionService } from '../services/AITaskSuggestionService';
import { NotificationService } from '../services/NotificationService';
import {
  AppSettings,
  DashboardWidget,
  HealthMetrics,
  IdentificationResult,
  NutritionEntry,
  SavedSearchFilter,
  ScanHistory,
  TodoItem,
  UserProfile
} from '../types';

interface AppState {
  // User data
  userProfile: UserProfile | null;
  scanHistory: ScanHistory[];
  todoItems: TodoItem[];
  dashboardWidgets: DashboardWidget[];
  savedSearchFilters: SavedSearchFilter[];
  appSettings: AppSettings;

  // App state
  isLoading: boolean;
  currentScan: IdentificationResult | null;
  
  // Actions
  setUserProfile: (profile: UserProfile) => void;
  addScanToHistory: (scan: ScanHistory) => void;
  updateScanInHistory: (id: string, updates: Partial<ScanHistory>) => void;
  deleteScanFromHistory: (id: string) => void;
  
  addTodoItem: (item: TodoItem) => void;
  updateTodoItem: (id: string, updates: Partial<TodoItem>) => void;
  deleteTodoItem: (id: string) => void;
  toggleTodoComplete: (id: string) => void;
  
  updateDashboardWidgets: (widgets: DashboardWidget[]) => void;

  // AI Task Suggestions
  generateAITasks: (scanResult: IdentificationResult) => void;

  // Search & Filters
  addSavedSearchFilter: (filter: SavedSearchFilter) => void;
  removeSavedSearchFilter: (id: string) => void;
  updateSavedSearchFilter: (id: string, updates: Partial<SavedSearchFilter>) => void;

  // Settings
  updateAppSettings: (settings: Partial<AppSettings>) => void;

  // Notifications
  scheduleTaskNotification: (todoItem: TodoItem) => Promise<void>;

  setCurrentScan: (scan: IdentificationResult | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Health integration
  nutritionEntries: NutritionEntry[];
  addNutritionEntry: (entry: NutritionEntry) => void;
  getNutritionEntries: (startDate?: Date, endDate?: Date) => NutritionEntry[];
  syncWithHealthKit: (scanResult: IdentificationResult) => Promise<void>;
  getHealthMetrics: (dateRange?: { start: Date; end: Date }) => Promise<HealthMetrics | null>;

  // Computed getters
  getRecentScans: (limit?: number) => ScanHistory[];
  getUpcomingTasks: (limit?: number) => TodoItem[];
  getScansByCategory: (category: string) => ScanHistory[];
  getCompletedTasksCount: () => number;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      userProfile: null,
      scanHistory: [],
      todoItems: [],
      dashboardWidgets: [
        { id: '1', type: 'recent_scans', title: 'Recent Scans', enabled: true, order: 1 },
        { id: '2', type: 'upcoming_tasks', title: 'Upcoming Tasks', enabled: true, order: 2 },
        { id: '3', type: 'achievements', title: 'Achievements', enabled: true, order: 3 },
        { id: '4', type: 'stats', title: 'Statistics', enabled: true, order: 4 },
      ],
      savedSearchFilters: [],
      nutritionEntries: [],
      appSettings: {
        theme: 'light',
        language: 'en',
        units: 'metric',
        notifications: {
          enabled: true,
          taskReminders: true,
          plantCareAlerts: true,
          achievementUnlocks: true,
          weeklySummaries: true,
          quietHoursEnabled: false,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          frequency: 'immediate',
        },
        privacy: {
          locationTracking: true,
          dataSharing: false,
          analytics: true,
        },
        display: {
          fontSize: 'medium',
          highContrast: false,
          reduceMotion: false,
        },
      },
      isLoading: false,
      currentScan: null,
      
      // Actions
      setUserProfile: (profile) => set({ userProfile: profile }),
      
      addScanToHistory: (scan) => set((state) => ({
        scanHistory: [{
          ...scan,
          result: {
            ...scan.result,
            timestamp: scan.result.timestamp
          }
        }, ...state.scanHistory]
      })),
      
      updateScanInHistory: (id, updates) => set((state) => ({
        scanHistory: state.scanHistory.map(scan => 
          scan.id === id ? { ...scan, ...updates } : scan
        )
      })),
      
      deleteScanFromHistory: (id) => set((state) => ({
        scanHistory: state.scanHistory.filter(scan => scan.id !== id)
      })),
      
      addTodoItem: (item) => set((state) => ({
        todoItems: [...state.todoItems, {
          ...item,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dueDate: item.dueDate ? new Date(item.dueDate).toISOString() : undefined
        }]
      })),
      
      updateTodoItem: (id, updates) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? {
            ...item,
            ...updates,
            updatedAt: new Date().toISOString()
          } : item
        )
      })),
      
      deleteTodoItem: (id) => set((state) => ({
        todoItems: state.todoItems.filter(item => item.id !== id)
      })),
      
      toggleTodoComplete: (id) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? {
            ...item,
            completed: !item.completed,
            updatedAt: new Date().toISOString()
          } : item
        )
      })),
      
      updateDashboardWidgets: (widgets) => set({ dashboardWidgets: widgets }),

      // AI Task Suggestions
      generateAITasks: (scanResult) => {
        const suggestedTasks = AITaskSuggestionService.generateTasksFromScan({
          ...scanResult,
          timestamp: new Date().toISOString()
        });
        
        set((state) => {
          // Validate tasks before adding to state
          const validTasks = suggestedTasks.filter((task): task is TodoItem =>
            !!task.id && !!task.title && !!task.dueDate
          );

          // Schedule notifications atomically with state update
          validTasks.forEach(async (task) => {
            const notificationId = await NotificationService.scheduleTaskReminder(
              task,
              state.appSettings.notifications
            );
            
            if (notificationId) {
              setTimeout(() => { // Use setTimeout to defer state update
                set((state) => ({
                  todoItems: state.todoItems.map(item =>
                    item.id === task.id ? { ...item, notificationId } : item
                  )
                }));
              }, 0);
            }
          });

          return { todoItems: [...state.todoItems, ...validTasks] };
        });
      },

      // Search & Filters
      addSavedSearchFilter: (filter) => set((state) => ({
        savedSearchFilters: [...state.savedSearchFilters, filter]
      })),

      removeSavedSearchFilter: (id) => set((state) => ({
        savedSearchFilters: state.savedSearchFilters.filter(filter => filter.id !== id)
      })),

      updateSavedSearchFilter: (id, updates) => set((state) => ({
        savedSearchFilters: state.savedSearchFilters.map(filter =>
          filter.id === id ? { ...filter, ...updates } : filter
        )
      })),

      // Settings
      updateAppSettings: (settings) => set((state) => ({
        appSettings: { ...state.appSettings, ...settings }
      })),

      // Notifications
      scheduleTaskNotification: async (todoItem) => {
        const { appSettings } = get();
        const notificationId = await NotificationService.scheduleTaskReminder(
          todoItem,
          appSettings.notifications
        );

        if (notificationId) {
          set((state) => ({
            todoItems: state.todoItems.map(item =>
              item.id === todoItem.id ? { ...item, notificationId } : item
            )
          }));
        }
      },

      setCurrentScan: (scan) => set({ currentScan: scan }),
      setLoading: (loading) => set({ isLoading: loading }),
      
      // Computed getters
      getRecentScans: (limit = 10) => {
        const { scanHistory } = get();
        return scanHistory
          .sort((a, b) =>
            new Date(b.result.timestamp).getTime() -
            new Date(a.result.timestamp).getTime()
          )
          .slice(0, limit);
      },
      
      getUpcomingTasks: (limit = 5) => {
        const { todoItems } = get();
        return todoItems
          .filter(item => !item.completed && item.dueDate)
          .sort((a, b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime())
          .slice(0, limit);
      },
      
      getScansByCategory: (category) => {
        const { scanHistory } = get();
        return scanHistory.filter(scan => scan.result.category === category);
      },
      
      getCompletedTasksCount: () => {
        const { todoItems } = get();
        return todoItems.filter(item => item.completed).length;
      },

      // Health integration methods
      addNutritionEntry: (entry) => {
        set((state) => ({
          nutritionEntries: [...state.nutritionEntries, entry],
        }));
      },

      getNutritionEntries: (startDate, endDate) => {
        const { nutritionEntries } = get();
        if (!startDate || !endDate) {
          return nutritionEntries;
        }
        return nutritionEntries.filter(entry => {
          const entryDate = new Date(entry.timestamp);
          return entryDate >= startDate && entryDate <= endDate;
        });
      },

      syncWithHealthKit: async (scanResult) => {
        try {
          if (scanResult.category === 'food') {
            const { HealthKitService } = await import('../services/HealthKitService');

            // Create nutrition entry
            const nutritionEntry: NutritionEntry = {
              id: `scan_${Date.now()}`,
              foodItem: scanResult.name,
              quantity: 1,
              unit: 'serving',
              timestamp: scanResult.timestamp,
              mealType: determineMealType(new Date(scanResult.timestamp)),
              nutrients: await estimateNutrients(scanResult.name),
              source: 'bioscan_app',
            };

            // Add to local store
            get().addNutritionEntry(nutritionEntry);

            // Sync with health platform
            if (HealthKitService.isHealthKitConnected()) {
              await HealthKitService.writeNutritionData(nutritionEntry);
            }
          }
        } catch (error) {
          console.error('Error syncing with HealthKit:', error);
        }
      },

      getHealthMetrics: async (dateRange) => {
        try {
          const { HealthKitService } = await import('../services/HealthKitService');

          if (!HealthKitService.isHealthKitConnected()) {
            return null;
          }

          const startDate = dateRange?.start || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          const endDate = dateRange?.end || new Date();

          return await HealthKitService.readHealthMetrics(startDate, endDate);
        } catch (error) {
          console.error('Error getting health metrics:', error);
          return null;
        }
      },
    }),
    {
      name: 'bioscan-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        userProfile: state.userProfile,
        scanHistory: state.scanHistory,
        todoItems: state.todoItems,
        dashboardWidgets: state.dashboardWidgets,
        savedSearchFilters: state.savedSearchFilters,
        appSettings: state.appSettings,
        nutritionEntries: state.nutritionEntries,
      }),
    }
  )
);

// Helper functions for health integration
function determineMealType(date: Date): 'breakfast' | 'lunch' | 'dinner' | 'snack' {
  const hour = date.getHours();

  if (hour >= 6 && hour < 11) return 'breakfast';
  if (hour >= 11 && hour < 15) return 'lunch';
  if (hour >= 17 && hour < 22) return 'dinner';
  return 'snack';
}

async function estimateNutrients(foodItem: string): Promise<Record<string, number>> {
  // Basic nutrition estimation - in a real app, you'd use a nutrition API
  const baseNutrients: Record<string, number> = {
    calories: 100,
    protein: 5,
    carbs: 20,
    fat: 3,
    fiber: 2,
    sugar: 10,
  };

  // Simple food-based adjustments
  const lowerFoodItem = foodItem.toLowerCase();

  if (lowerFoodItem.includes('fruit') || lowerFoodItem.includes('apple') || lowerFoodItem.includes('banana')) {
    return {
      calories: 80,
      protein: 1,
      carbs: 20,
      fat: 0.3,
      fiber: 3,
      sugar: 15,
    };
  }

  if (lowerFoodItem.includes('vegetable') || lowerFoodItem.includes('broccoli') || lowerFoodItem.includes('carrot')) {
    return {
      calories: 25,
      protein: 2,
      carbs: 5,
      fat: 0.1,
      fiber: 3,
      sugar: 2,
    };
  }

  return baseNutrients;
}
