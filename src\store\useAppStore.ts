import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  ScanHistory, 
  UserProfile, 
  TodoItem, 
  DashboardWidget, 
  IdentificationResult 
} from '../types';

interface AppState {
  // User data
  userProfile: UserProfile | null;
  scanHistory: ScanHistory[];
  todoItems: TodoItem[];
  dashboardWidgets: DashboardWidget[];
  
  // App state
  isLoading: boolean;
  currentScan: IdentificationResult | null;
  
  // Actions
  setUserProfile: (profile: UserProfile) => void;
  addScanToHistory: (scan: ScanHistory) => void;
  updateScanInHistory: (id: string, updates: Partial<ScanHistory>) => void;
  deleteScanFromHistory: (id: string) => void;
  
  addTodoItem: (item: TodoItem) => void;
  updateTodoItem: (id: string, updates: Partial<TodoItem>) => void;
  deleteTodoItem: (id: string) => void;
  toggleTodoComplete: (id: string) => void;
  
  updateDashboardWidgets: (widgets: DashboardWidget[]) => void;
  
  setCurrentScan: (scan: IdentificationResult | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Computed getters
  getRecentScans: (limit?: number) => ScanHistory[];
  getUpcomingTasks: (limit?: number) => TodoItem[];
  getScansByCategory: (category: string) => ScanHistory[];
  getCompletedTasksCount: () => number;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      userProfile: null,
      scanHistory: [],
      todoItems: [],
      dashboardWidgets: [
        { id: '1', type: 'recent_scans', title: 'Recent Scans', enabled: true, order: 1 },
        { id: '2', type: 'upcoming_tasks', title: 'Upcoming Tasks', enabled: true, order: 2 },
        { id: '3', type: 'achievements', title: 'Achievements', enabled: true, order: 3 },
        { id: '4', type: 'stats', title: 'Statistics', enabled: true, order: 4 },
      ],
      isLoading: false,
      currentScan: null,
      
      // Actions
      setUserProfile: (profile) => set({ userProfile: profile }),
      
      addScanToHistory: (scan) => set((state) => ({
        scanHistory: [scan, ...state.scanHistory]
      })),
      
      updateScanInHistory: (id, updates) => set((state) => ({
        scanHistory: state.scanHistory.map(scan => 
          scan.id === id ? { ...scan, ...updates } : scan
        )
      })),
      
      deleteScanFromHistory: (id) => set((state) => ({
        scanHistory: state.scanHistory.filter(scan => scan.id !== id)
      })),
      
      addTodoItem: (item) => set((state) => ({
        todoItems: [...state.todoItems, item]
      })),
      
      updateTodoItem: (id, updates) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? { ...item, ...updates, updatedAt: new Date() } : item
        )
      })),
      
      deleteTodoItem: (id) => set((state) => ({
        todoItems: state.todoItems.filter(item => item.id !== id)
      })),
      
      toggleTodoComplete: (id) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? { ...item, completed: !item.completed, updatedAt: new Date() } : item
        )
      })),
      
      updateDashboardWidgets: (widgets) => set({ dashboardWidgets: widgets }),
      
      setCurrentScan: (scan) => set({ currentScan: scan }),
      setLoading: (loading) => set({ isLoading: loading }),
      
      // Computed getters
      getRecentScans: (limit = 10) => {
        const { scanHistory } = get();
        return scanHistory
          .sort((a, b) => new Date(b.result.timestamp).getTime() - new Date(a.result.timestamp).getTime())
          .slice(0, limit);
      },
      
      getUpcomingTasks: (limit = 5) => {
        const { todoItems } = get();
        return todoItems
          .filter(item => !item.completed && item.dueDate)
          .sort((a, b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime())
          .slice(0, limit);
      },
      
      getScansByCategory: (category) => {
        const { scanHistory } = get();
        return scanHistory.filter(scan => scan.result.category === category);
      },
      
      getCompletedTasksCount: () => {
        const { todoItems } = get();
        return todoItems.filter(item => item.completed).length;
      },
    }),
    {
      name: 'bioscan-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        userProfile: state.userProfile,
        scanHistory: state.scanHistory,
        todoItems: state.todoItems,
        dashboardWidgets: state.dashboardWidgets,
      }),
    }
  )
);
