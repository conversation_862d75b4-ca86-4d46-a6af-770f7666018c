import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import {
    DashboardWidget,
    IdentificationResult,
    ScanHistory,
    TodoItem,
    UserProfile
} from '../types';

interface AppState {
  // User data
  userProfile: UserProfile | null;
  scanHistory: ScanHistory[];
  todoItems: TodoItem[];
  dashboardWidgets: DashboardWidget[];
  savedSearchFilters: SavedSearchFilter[];
  appSettings: AppSettings;

  // App state
  isLoading: boolean;
  currentScan: IdentificationResult | null;
  
  // Actions
  setUserProfile: (profile: UserProfile) => void;
  addScanToHistory: (scan: ScanHistory) => void;
  updateScanInHistory: (id: string, updates: Partial<ScanHistory>) => void;
  deleteScanFromHistory: (id: string) => void;
  
  addTodoItem: (item: TodoItem) => void;
  updateTodoItem: (id: string, updates: Partial<TodoItem>) => void;
  deleteTodoItem: (id: string) => void;
  toggleTodoComplete: (id: string) => void;
  
  updateDashboardWidgets: (widgets: DashboardWidget[]) => void;

  // AI Task Suggestions
  generateAITasks: (scanResult: IdentificationResult) => void;

  // Search & Filters
  addSavedSearchFilter: (filter: SavedSearchFilter) => void;
  removeSavedSearchFilter: (id: string) => void;
  updateSavedSearchFilter: (id: string, updates: Partial<SavedSearchFilter>) => void;

  // Settings
  updateAppSettings: (settings: Partial<AppSettings>) => void;

  // Notifications
  scheduleTaskNotification: (todoItem: TodoItem) => Promise<void>;

  setCurrentScan: (scan: IdentificationResult | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Computed getters
  getRecentScans: (limit?: number) => ScanHistory[];
  getUpcomingTasks: (limit?: number) => TodoItem[];
  getScansByCategory: (category: string) => ScanHistory[];
  getCompletedTasksCount: () => number;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      userProfile: null,
      scanHistory: [],
      todoItems: [],
      dashboardWidgets: [
        { id: '1', type: 'recent_scans', title: 'Recent Scans', enabled: true, order: 1 },
        { id: '2', type: 'upcoming_tasks', title: 'Upcoming Tasks', enabled: true, order: 2 },
        { id: '3', type: 'achievements', title: 'Achievements', enabled: true, order: 3 },
        { id: '4', type: 'stats', title: 'Statistics', enabled: true, order: 4 },
      ],
      savedSearchFilters: [],
      appSettings: {
        theme: 'light',
        language: 'en',
        units: 'metric',
        notifications: {
          enabled: true,
          taskReminders: true,
          plantCareAlerts: true,
          achievementUnlocks: true,
          weeklySummaries: true,
          quietHoursEnabled: false,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          frequency: 'immediate',
        },
        privacy: {
          locationTracking: true,
          dataSharing: false,
          analytics: true,
        },
        display: {
          fontSize: 'medium',
          highContrast: false,
          reduceMotion: false,
        },
      },
      isLoading: false,
      currentScan: null,
      
      // Actions
      setUserProfile: (profile) => set({ userProfile: profile }),
      
      addScanToHistory: (scan) => set((state) => ({
        scanHistory: [scan, ...state.scanHistory]
      })),
      
      updateScanInHistory: (id, updates) => set((state) => ({
        scanHistory: state.scanHistory.map(scan => 
          scan.id === id ? { ...scan, ...updates } : scan
        )
      })),
      
      deleteScanFromHistory: (id) => set((state) => ({
        scanHistory: state.scanHistory.filter(scan => scan.id !== id)
      })),
      
      addTodoItem: (item) => set((state) => ({
        todoItems: [...state.todoItems, item]
      })),
      
      updateTodoItem: (id, updates) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? { ...item, ...updates, updatedAt: new Date() } : item
        )
      })),
      
      deleteTodoItem: (id) => set((state) => ({
        todoItems: state.todoItems.filter(item => item.id !== id)
      })),
      
      toggleTodoComplete: (id) => set((state) => ({
        todoItems: state.todoItems.map(item => 
          item.id === id ? { ...item, completed: !item.completed, updatedAt: new Date() } : item
        )
      })),
      
      updateDashboardWidgets: (widgets) => set({ dashboardWidgets: widgets }),

      // AI Task Suggestions
      generateAITasks: (scanResult) => {
        const suggestedTasks = AITaskSuggestionService.generateTasksFromScan(scanResult);
        set((state) => ({
          todoItems: [...state.todoItems, ...suggestedTasks]
        }));

        // Schedule notifications for the new tasks
        suggestedTasks.forEach(async (task) => {
          if (task.dueDate) {
            const notificationId = await NotificationService.scheduleTaskReminder(
              task,
              state.appSettings.notifications
            );
            if (notificationId) {
              // Update task with notification ID
              set((state) => ({
                todoItems: state.todoItems.map(item =>
                  item.id === task.id ? { ...item, notificationId } : item
                )
              }));
            }
          }
        });
      },

      // Search & Filters
      addSavedSearchFilter: (filter) => set((state) => ({
        savedSearchFilters: [...state.savedSearchFilters, filter]
      })),

      removeSavedSearchFilter: (id) => set((state) => ({
        savedSearchFilters: state.savedSearchFilters.filter(filter => filter.id !== id)
      })),

      updateSavedSearchFilter: (id, updates) => set((state) => ({
        savedSearchFilters: state.savedSearchFilters.map(filter =>
          filter.id === id ? { ...filter, ...updates } : filter
        )
      })),

      // Settings
      updateAppSettings: (settings) => set((state) => ({
        appSettings: { ...state.appSettings, ...settings }
      })),

      // Notifications
      scheduleTaskNotification: async (todoItem) => {
        const { appSettings } = get();
        const notificationId = await NotificationService.scheduleTaskReminder(
          todoItem,
          appSettings.notifications
        );

        if (notificationId) {
          set((state) => ({
            todoItems: state.todoItems.map(item =>
              item.id === todoItem.id ? { ...item, notificationId } : item
            )
          }));
        }
      },

      setCurrentScan: (scan) => set({ currentScan: scan }),
      setLoading: (loading) => set({ isLoading: loading }),
      
      // Computed getters
      getRecentScans: (limit = 10) => {
        const { scanHistory } = get();
        return scanHistory
          .sort((a, b) => new Date(b.result.timestamp).getTime() - new Date(a.result.timestamp).getTime())
          .slice(0, limit);
      },
      
      getUpcomingTasks: (limit = 5) => {
        const { todoItems } = get();
        return todoItems
          .filter(item => !item.completed && item.dueDate)
          .sort((a, b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime())
          .slice(0, limit);
      },
      
      getScansByCategory: (category) => {
        const { scanHistory } = get();
        return scanHistory.filter(scan => scan.result.category === category);
      },
      
      getCompletedTasksCount: () => {
        const { todoItems } = get();
        return todoItems.filter(item => item.completed).length;
      },
    }),
    {
      name: 'bioscan-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        userProfile: state.userProfile,
        scanHistory: state.scanHistory,
        todoItems: state.todoItems,
        dashboardWidgets: state.dashboardWidgets,
        savedSearchFilters: state.savedSearchFilters,
        appSettings: state.appSettings,
      }),
    }
  )
);
