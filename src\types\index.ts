// Core types for BioScan app

export interface IdentificationResult {
  id: string;
  category: 'food' | 'plants' | 'animals' | 'rocks' | 'coins';
  name: string;
  scientificName?: string;
  confidence: number;
  description: string;
  image?: string;
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  additionalInfo?: Record<string, any>;
}

export interface ScanHistory {
  id: string;
  result: IdentificationResult;
  originalImage: string;
  notes?: string;
  tags?: string[];
  isFavorite: boolean;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  scanCount: number;
  achievements: Achievement[];
  preferences: UserPreferences;
  createdAt: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  category: string;
}

export interface UserPreferences {
  notifications: boolean;
  locationTracking: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
}

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high';
  category: string;
  relatedScanId?: string;
  reminders?: Date[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardWidget {
  id: string;
  type: 'recent_scans' | 'upcoming_tasks' | 'achievements' | 'stats' | 'weather';
  title: string;
  enabled: boolean;
  order: number;
}

export interface SearchFilters {
  category?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: string;
  tags?: string[];
  confidence?: {
    min: number;
    max: number;
  };
}

export interface GeminiResponse {
  identification: {
    name: string;
    scientificName?: string;
    category: string;
    confidence: number;
    alternatives?: Array<{
      name: string;
      confidence: number;
    }>;
  };
  information: {
    description: string;
    facts: string[];
    care?: any;
    nutrition?: any;
    habitat?: any;
    properties?: any;
    value?: any;
  };
}

export interface CameraSettings {
  flashMode: 'on' | 'off' | 'auto';
  quality: number;
  ratio: string;
  zoom: number;
}
