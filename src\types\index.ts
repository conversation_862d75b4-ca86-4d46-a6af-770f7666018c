// Core types for BioScan app

export interface IdentificationResult {
  id: string;
  category: 'food' | 'plants' | 'animals' | 'rocks' | 'coins';
  name: string;
  scientificName?: string;
  confidence: number;
  description: string;
  image?: string;
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  additionalInfo?: Record<string, any>;
}

export interface ScanHistory {
  id: string;
  result: IdentificationResult;
  originalImage: string;
  notes?: string;
  tags?: string[];
  isFavorite: boolean;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  scanCount: number;
  achievements: Achievement[];
  preferences: UserPreferences;
  createdAt: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  category: string;
}

export interface UserPreferences {
  notifications: boolean;
  locationTracking: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
}

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  dueDate?: string;
  dueTime?: string; // HH:MM format
  priority: 'low' | 'medium' | 'high';
  category: string;
  relatedScanId?: string;
  reminders?: Date[];
  isRecurring?: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  recurringInterval?: number; // e.g., every 2 days
  isAISuggested?: boolean;
  tags?: string[];
  notificationId?: string;
  createdAt: string;
  updatedAt: string;
}

// NOTE: The previous DashboardWidget interface here was likely a simplified version.
// The error refers to 'DashboardWidgetConfig', which is defined further down.
// I'm keeping this one as is, assuming it's a separate type if both exist.
export interface DashboardWidget {
  id: string;
  type: 'recent_scans' | 'upcoming_tasks' | 'achievements' | 'stats' | 'weather';
  title: string;
  enabled: boolean;
  order: number;
}

export interface SearchFilters {
  category?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: string;
  tags?: string[];
  confidence?: {
    min: number;
    max: number;
  };
}

export interface GeminiResponse {
  identification: {
    name: string;
    scientificName?: string;
    category: string;
    confidence: number;
    alternatives?: Array<{
      name: string;
      confidence: number;
    }>;
  };
  information: {
    description: string;
    facts: string[];
    care?: any;
    nutrition?: any;
    habitat?: any;
    properties?: any;
    value?: any;
  };
}

export interface CameraSettings {
  flashMode: 'on' | 'off' | 'auto';
  quality: number;
  ratio: string;
  zoom: number;
}

export interface NotificationSettings {
  enabled: boolean;
  taskReminders: boolean;
  plantCareAlerts: boolean;
  achievementUnlocks: boolean;
  weeklySummaries: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string; // HH:MM format
  quietHoursEnd: string; // HH:MM format
  frequency: 'immediate' | 'daily' | 'weekly';
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
  notifications: NotificationSettings;
  privacy: {
    locationTracking: boolean;
    dataSharing: boolean;
    analytics: boolean;
  };
  display: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    reduceMotion: boolean;
  };
}

export interface SavedSearchFilter {
  id: string;
  name: string;
  filters: SearchFilters;
  createdAt: Date;
}

export interface DashboardWidgetConfig {
  id: string;
  type: 'recent_scans' | 'upcoming_tasks' | 'achievements' | 'stats' | 'weather' | 'categories';
  title: string;
  enabled: boolean;
  order: number;
  size: 'small' | 'medium' | 'large';
  // FIX: Added 'width' and 'height' to the 'position' object
  position: { x: number; y: number; width: number; height: number; }; // <--- THIS IS THE CRUCIAL CHANGE
}

export interface WidgetData {
  lastUpdated: string;
  stats: {
    totalScans: number;
    recentScansCount: number;
    pendingTasks: number;
    completedTasks: number;
  };
  recentScans: Array<{
    id: string;
    name: string;
    category: string;
    timestamp: string;
  }>;
  upcomingTasks: Array<{
    id: string;
    title: string;
    dueDate?: string;
    priority: string;
  }>;
  achievements: Array<{
    title: string;
    description: string;
    unlocked: boolean;
  }>;
  quickActions: Array<{
    id: string;
    title: string;
    icon: string;
    action: string;
  }>;
}

export interface WidgetConfig {
  showStats: boolean;
  showRecentScans: boolean;
  showUpcomingTasks: boolean;
  showAchievements: boolean;
  showQuickActions: boolean;
  maxRecentScans: number;
  maxUpcomingTasks: number;
  refreshInterval: number;
  theme: 'light' | 'dark' | 'auto';
}

// Health Kit Types
export interface NutritionEntry {
  id: string;
  foodItem: string;
  quantity: number;
  unit: string;
  timestamp: string; // Already correct as string
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  nutrients?: Record<string, number>;
  source: string;
}

export interface HealthMetrics {
  calories: {
    consumed: number;
    burned: number;
    net: number;
  };
  macronutrients: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  hydration?: {
    water: number;
    goal: number;
  };
  activity: {
    steps: number;
    activeMinutes: number;
    distance: number;
  };
  weight?: number;
  bodyFat?: number;
}

export interface HealthData {
  date: string;
  metrics: HealthMetrics;
}

export interface FitnessData {
  steps: number;
  calories: number;
  distance: number;
  activeMinutes: number;
  heartRate?: {
    average: number;
    resting: number;
    max: number;
  };
  sleep?: {
    duration: number;
    quality: string;
  };
}

// Product Research Types
export interface ProductSearchResult {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  currency: string;
  rating: number;
  reviewCount: number;
  availability: string;
  retailer: string;
  url: string;
  category: string;
  brand: string;
}

export interface ProductInfo {
  id: string;
  name: string;
  description: string;
  images: string[];
  category: string;
  brand: string;
  model: string;
  specifications: Record<string, string>;
  features: string[];
  priceRange: {
    min: number;
    max: number;
    currency: string;
  };
  availability: {
    inStock: boolean;
    estimatedDelivery: string;
    stockCount: number;
  };
  countryOfOrigin: string;
  manufacturer: {
    name: string;
    website: string;
    contact: string;
  };
  certifications: string[];
  sustainabilityScore: number;
  carbonFootprint: string;
}

export interface PriceComparison {
  retailer: string;
  price: number;
  currency: string;
  availability: string;
  shipping: string;
  rating: number;
  reviewCount: number;
  url: string;
  lastUpdated: string;
}

export interface ProductReview {
  id: string;
  author: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  source: string;
  verified: boolean;
  helpful: number;
}

// Third Party Integration Types
export interface ThirdPartyApp {
  id: string;
  name: string;
  category: 'productivity' | 'communication' | 'notes' | 'health';
  icon: string;
  description: string;
  urlScheme: string;
  webUrl: string;
  apiEndpoint?: string;
  requiresAuth: boolean;
  features: string[];
  accessToken?: string;
}

export interface IntegrationConfig {
  appId: string;
  enabled: boolean;
  autoSync: boolean;
  syncSettings: {
    syncTasks: boolean;
    syncScans: boolean;
    syncAchievements: boolean;
  };
}
