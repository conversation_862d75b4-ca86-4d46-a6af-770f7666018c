// Core types for BioScan app

export interface IdentificationResult {
  id: string;
  category: 'food' | 'plants' | 'animals' | 'rocks' | 'coins';
  name: string;
  scientificName?: string;
  confidence: number;
  description: string;
  image?: string;
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  additionalInfo?: Record<string, any>;
}

export interface ScanHistory {
  id: string;
  result: IdentificationResult;
  originalImage: string;
  notes?: string;
  tags?: string[];
  isFavorite: boolean;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  scanCount: number;
  achievements: Achievement[];
  preferences: UserPreferences;
  createdAt: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  category: string;
}

export interface UserPreferences {
  notifications: boolean;
  locationTracking: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
}

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  dueDate?: Date;
  dueTime?: string; // HH:MM format
  priority: 'low' | 'medium' | 'high';
  category: string;
  relatedScanId?: string;
  reminders?: Date[];
  isRecurring?: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  recurringInterval?: number; // e.g., every 2 days
  isAISuggested?: boolean;
  tags?: string[];
  notificationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// NOTE: The previous DashboardWidget interface here was likely a simplified version.
// The error refers to 'DashboardWidgetConfig', which is defined further down.
// I'm keeping this one as is, assuming it's a separate type if both exist.
export interface DashboardWidget {
  id: string;
  type: 'recent_scans' | 'upcoming_tasks' | 'achievements' | 'stats' | 'weather';
  title: string;
  enabled: boolean;
  order: number;
}

export interface SearchFilters {
  category?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: string;
  tags?: string[];
  confidence?: {
    min: number;
    max: number;
  };
}

export interface GeminiResponse {
  identification: {
    name: string;
    scientificName?: string;
    category: string;
    confidence: number;
    alternatives?: Array<{
      name: string;
      confidence: number;
    }>;
  };
  information: {
    description: string;
    facts: string[];
    care?: any;
    nutrition?: any;
    habitat?: any;
    properties?: any;
    value?: any;
  };
}

export interface CameraSettings {
  flashMode: 'on' | 'off' | 'auto';
  quality: number;
  ratio: string;
  zoom: number;
}

export interface NotificationSettings {
  enabled: boolean;
  taskReminders: boolean;
  plantCareAlerts: boolean;
  achievementUnlocks: boolean;
  weeklySummaries: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string; // HH:MM format
  quietHoursEnd: string; // HH:MM format
  frequency: 'immediate' | 'daily' | 'weekly';
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  units: 'metric' | 'imperial';
  notifications: NotificationSettings;
  privacy: {
    locationTracking: boolean;
    dataSharing: boolean;
    analytics: boolean;
  };
  display: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    reduceMotion: boolean;
  };
}

export interface SavedSearchFilter {
  id: string;
  name: string;
  filters: SearchFilters;
  createdAt: Date;
}

export interface DashboardWidgetConfig {
  id: string;
  type: 'recent_scans' | 'upcoming_tasks' | 'achievements' | 'stats' | 'weather' | 'categories';
  title: string;
  enabled: boolean;
  order: number;
  size: 'small' | 'medium' | 'large';
  // FIX: Added 'width' and 'height' to the 'position' object
  position: { x: number; y: number; width: number; height: number; }; // <--- THIS IS THE CRUCIAL CHANGE
}
