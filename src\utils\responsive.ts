import { Dimensions, Platform, PixelRatio } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Device type detection
export const DeviceType = {
  PHONE: 'phone',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
} as const;

export type DeviceTypeValue = typeof DeviceType[keyof typeof DeviceType];

// Breakpoints (in dp)
export const Breakpoints = {
  SMALL: 480,
  MEDIUM: 768,
  LARGE: 1024,
  XLARGE: 1200,
} as const;

/**
 * Get current device type based on screen dimensions
 */
export const getDeviceType = (): DeviceTypeValue => {
  if (screenWidth >= Breakpoints.LARGE) {
    return DeviceType.DESKTOP;
  } else if (screenWidth >= Breakpoints.MEDIUM) {
    return DeviceType.TABLET;
  } else {
    return DeviceType.PHONE;
  }
};

/**
 * Check if current device is a tablet
 */
export const isTablet = (): boolean => {
  return getDeviceType() === DeviceType.TABLET;
};

/**
 * Check if current device is a phone
 */
export const isPhone = (): boolean => {
  return getDeviceType() === DeviceType.PHONE;
};

/**
 * Check if current device is desktop/large screen
 */
export const isDesktop = (): boolean => {
  return getDeviceType() === DeviceType.DESKTOP;
};

/**
 * Get responsive value based on device type
 */
export const getResponsiveValue = <T>(values: {
  phone: T;
  tablet?: T;
  desktop?: T;
}): T => {
  const deviceType = getDeviceType();
  
  switch (deviceType) {
    case DeviceType.DESKTOP:
      return values.desktop ?? values.tablet ?? values.phone;
    case DeviceType.TABLET:
      return values.tablet ?? values.phone;
    default:
      return values.phone;
  }
};

/**
 * Scale size based on screen width
 */
export const scaleSize = (size: number): number => {
  const baseWidth = 375; // iPhone X width as base
  return (screenWidth / baseWidth) * size;
};

/**
 * Scale font size based on screen width and pixel density
 */
export const scaleFontSize = (size: number): number => {
  const scale = screenWidth / 320;
  const newSize = size * scale;
  
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
  }
};

/**
 * Get responsive padding based on device type
 */
export const getResponsivePadding = () => {
  return getResponsiveValue({
    phone: 16,
    tablet: 24,
    desktop: 32,
  });
};

/**
 * Get responsive margin based on device type
 */
export const getResponsiveMargin = () => {
  return getResponsiveValue({
    phone: 12,
    tablet: 16,
    desktop: 20,
  });
};

/**
 * Get responsive border radius based on device type
 */
export const getResponsiveBorderRadius = () => {
  return getResponsiveValue({
    phone: 8,
    tablet: 12,
    desktop: 16,
  });
};

/**
 * Get responsive icon size based on device type
 */
export const getResponsiveIconSize = (baseSize: number = 24) => {
  return getResponsiveValue({
    phone: baseSize,
    tablet: baseSize + 4,
    desktop: baseSize + 8,
  });
};

/**
 * Get responsive font size based on device type
 */
export const getResponsiveFontSize = (baseSize: number) => {
  return getResponsiveValue({
    phone: scaleFontSize(baseSize),
    tablet: scaleFontSize(baseSize + 2),
    desktop: scaleFontSize(baseSize + 4),
  });
};

/**
 * Get grid columns based on device type
 */
export const getGridColumns = (baseColumns: number = 2) => {
  return getResponsiveValue({
    phone: baseColumns,
    tablet: baseColumns + 1,
    desktop: baseColumns + 2,
  });
};

/**
 * Get responsive layout configuration
 */
export const getLayoutConfig = () => {
  const deviceType = getDeviceType();
  
  return {
    deviceType,
    isTablet: deviceType === DeviceType.TABLET,
    isPhone: deviceType === DeviceType.PHONE,
    isDesktop: deviceType === DeviceType.DESKTOP,
    screenWidth,
    screenHeight,
    padding: getResponsivePadding(),
    margin: getResponsiveMargin(),
    borderRadius: getResponsiveBorderRadius(),
    columns: getGridColumns(),
    // Layout patterns
    useSideNavigation: deviceType !== DeviceType.PHONE,
    useSplitView: deviceType === DeviceType.TABLET || deviceType === DeviceType.DESKTOP,
    useModalAsFullScreen: deviceType === DeviceType.PHONE,
    // Touch targets
    minTouchTarget: deviceType === DeviceType.PHONE ? 44 : 48,
    // Typography
    baseFontSize: getResponsiveFontSize(16),
    headingFontSize: getResponsiveFontSize(24),
    captionFontSize: getResponsiveFontSize(12),
  };
};

/**
 * Platform-specific styles helper
 */
export const platformStyles = {
  ios: (styles: any) => Platform.OS === 'ios' ? styles : {},
  android: (styles: any) => Platform.OS === 'android' ? styles : {},
  web: (styles: any) => Platform.OS === 'web' ? styles : {},
};

/**
 * Safe area insets for different devices
 */
export const getSafeAreaInsets = () => {
  const deviceType = getDeviceType();
  
  // These would typically come from react-native-safe-area-context
  // For now, providing reasonable defaults
  return {
    top: getResponsiveValue({
      phone: Platform.OS === 'ios' ? 44 : 24,
      tablet: Platform.OS === 'ios' ? 24 : 24,
      desktop: 0,
    }),
    bottom: getResponsiveValue({
      phone: Platform.OS === 'ios' ? 34 : 0,
      tablet: 0,
      desktop: 0,
    }),
    left: 0,
    right: 0,
  };
};

/**
 * Responsive dimensions helper
 */
export const responsiveDimensions = {
  width: (percentage: number) => (screenWidth * percentage) / 100,
  height: (percentage: number) => (screenHeight * percentage) / 100,
  maxWidth: getResponsiveValue({
    phone: screenWidth,
    tablet: Math.min(screenWidth, 768),
    desktop: Math.min(screenWidth, 1200),
  }),
};

/**
 * Animation duration based on device performance
 */
export const getAnimationDuration = (baseDuration: number = 300) => {
  // Reduce animation duration on lower-end devices
  const pixelRatio = PixelRatio.get();
  const performanceMultiplier = pixelRatio > 2 ? 1 : 0.8;
  
  return Math.round(baseDuration * performanceMultiplier);
};

/**
 * Haptic feedback intensity based on device type
 */
export const getHapticIntensity = () => {
  return getResponsiveValue({
    phone: 'medium',
    tablet: 'light',
    desktop: 'none',
  });
};
